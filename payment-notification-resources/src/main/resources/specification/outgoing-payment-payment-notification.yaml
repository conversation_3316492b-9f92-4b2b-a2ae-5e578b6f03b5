openapi: 3.0.3
info:
  title: E-Transfer Payment Processing Event
  description: 'Business Event'
  version: 1.0.0
  x-version-history:
    '1.0.0': 2024-04-04 - First Version
tags:
  - name: E-Transfer Payment Processing Event
    description: Event definition for all payment notification related to Etransfer Domestic

paths: { }

components:
  schemas:
    ETransferPaymentProcessingEvent:
      description: Domestic E-Transfer Payment Processing Event
      type: object
      required:
        - version
        - entity
        - eventType
        - eventTime
        - instructionIdentification
        - endToEndBusinessIdentification
        - channelId
        - clientId
        - paymentType
        - rawData
      properties:
        version:
          $ref: '#/components/schemas/Version'
        entity:
          $ref: '#/components/schemas/Entity'
        eventType:
          $ref: '#/components/schemas/EventType'
        eventTime:
          $ref: '#/components/schemas/EventTime'
        instructionIdentification:
          $ref: '#/components/schemas/InstructionIdentification'
        endToEndBusinessIdentification:
          $ref: '#/components/schemas/EndToEndBusinessIdentification'
        channelId:
          $ref: '#/components/schemas/ChannelId'
        channelType:
          $ref: '#/components/schemas/ChannelType'
        clientId:
          $ref: '#/components/schemas/ClientId'
        partnerId:
          $ref: '#/components/schemas/PartnerId'
        agentId:
          $ref: '#/components/schemas/AgentId'
        paymentType:
          $ref: '#/components/schemas/LocalInstrument2Choice'
        msgDefIdr:
          $ref: '#/components/schemas/MsgDefIdr'
        rawData:
          $ref: '#/components/schemas/PaymentEventRawData'
    Version:
      description: >-
        Version of the microservice that originally created the event represented by a standard versioning format [Major].[Minor].[Patch].
      type: string
      example: '1.0.3'
    Entity:
      type: string
      description: >-
        Represents the asset (CRUD) or the business capacity (Payment transaction) of the event.
      enum:
        - DOMESTIC_ETRANSFER_PAYMENT
    EventType:
      type: string
      enum:
        - PAYMENT-REJECTED
        - PAYMENT-SUCCEEDED
      description: >-
        Type of business event.
      example: "PAYMENT-REJECTED"
    EventTime:
      type: string
      format: date-time
      description: >-
        Represents the "User Action Date" (time of the event not the publication date).
      example: 2021-08-28T09:24:30.000Z
    InstructionIdentification:
      type: string
      description: >-
        InstructionIdentification is a unique transaction identifier auto generated by the consumer. One InstructionId per request. Recommended a uuid without dashes.
      example: "95D2E46DDC124510BFEAB5C268A206B2"
      minLength: 1
      maxLength: 35
    EndToEndBusinessIdentification:
      type: string
      minLength: 1
      maxLength: 35
      description: >-
        Business transaction identifier used across the FI systems (end-to-end chain).
      example: 'ADA78940643E4D70BE143BBB36992095'
    ChannelId:
      maxLength: 4
      minLength: 1
      type: string
      description: >-
        The identification of the asset at the origin of the publication.
      example: "6256"
    ChannelType:
      description: >-
        Indicates the channel type.
      type: string
      enum: [ 'MOBILE','WEB','INTERAC' ]
      example: 'MOBILE'
    ClientId:
      type: string
      description: >-
        The identification number of the client within the FI system  (ex.: bncId) who initiated the event.
      example: "2C811E20AEE80A2860764937595FFE76DBF72788941F0C7726CC626949350900"
    PartnerId:
      type: string
      description: >-
        Identifier of the partnerId using NBC services.
      example: "PARTNERSHIP"
    ClientType:
      type: string
      description: >-
        The type of client who initiated the payment instruction. Retail/Individual transactions vs Commercial/Organisation transaction (I, O)
      enum:
        - INDIVIDUAL
        - ORGANIZATION
      example: 'INDIVIDUAL'
    LocalInstrument2Choice:
      type: object
      properties:
        proprietary:
          description: >-
            Type of payment </br>
            REGULAR_PAYMENT                  - Regular "Send-Money" payment </br>
            FULFILL_REQUEST_FOR_PAYMENT      - Fulfill Money Request payment </br>
            ACCOUNT_ALIAS_PAYMENT            - Auto-Deposit payment </br>
            REALTIME_ACCOUNT_ALIAS_PAYMENT   - Real-time Auto-Deposit payment </br>
            ACCOUNT_DEPOSIT_PAYMENT          - Account-Deposit payment </br>
            REALTIME_ACCOUNT_DEPOSIT_PAYMENT - Real-time Account-Deposit payment </br>
          type: string
          enum:
            - REGULAR_PAYMENT
            - FULFILL_REQUEST_FOR_PAYMENT
            - ACCOUNT_ALIAS_PAYMENT
            - REALTIME_ACCOUNT_ALIAS_PAYMENT
            - ACCOUNT_DEPOSIT_PAYMENT
            - REALTIME_ACCOUNT_DEPOSIT_PAYMENT
          x-example: REGULAR_PAYMENT
      required:
        - proprietary
    MsgDefIdr:
      type: string
      description: >-
        Specifies the rawData content. For example, it can contain the MessageIdentifier which defines the Business message(PIM)
      example: "pacs.008.001.08"
    PaymentEventRawData:
      title: PaymentEventRawData
      type: object
      required:
        - sendPaymentRequest
      properties:
        eTransferId:
          $ref: '#/components/schemas/ClearingSystemReference'
        interacUserId:
          description:  Customer ID for the client in interac side, and Customer must be registered in the e-Transfer system.
          type: string
          minLength: 1
          maxLength: 36
          example: "InteracUserIdMaxLength36"
        sendPaymentRequest:
          $ref: '#/components/schemas/SendPaymentRequest'
        transactionStatus: # TxSts
          $ref: '#/components/schemas/ExternalPaymentTransactionStatus1Code'
        statusReasonInformation:
          type: object
          description: Provides detailed information on the status reason.
          properties:
            code:
              type: string
              description: Reason for the status.
              minLength: 1
              maxLength: 50
              example: "AC04" # Closed Account Number
            proprietary:
              type: string
              description: Reason for the status, in a proprietary form.
              minLength: 1
              maxLength: 100
              example: "ACCNT_CLOSED" # Account number specified has been closed
    ISODateTime:
      type: string
      description: >-
        A particular point in the progression of time defined and expressed in either
        UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format
        (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM- DDThh:mm:ss.sss).
        These representations are defined in XML Schema Part 2: Datatypes Second Edition
        - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
      format: date-time
      example: '2020-01-23T12:34:56.000Z'
    ClearingSystemReference:
      description: The Interac-generated payment or request for payment reference
        numbers, also known as the clearing system reference in ISO 20022.
      type: string
      minLength: 8
      maxLength: 35
      example: ****************
    SendPaymentRequest:
      type: object
      required:
        - FIToFICustomerCreditTransfer
      properties:
        FIToFICustomerCreditTransfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransferV08'
    AccountIdentification4Choice:
      description: >-
        Account identification wrapper element.
      type: object
      required:
        - other
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification1'
    ActiveCurrencyAndAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          $ref: '#/components/schemas/ActiveAmount_SimpleType'
        currency:
          $ref: '#/components/schemas/ActiveCurrencyCode'
    ActiveAmount_SimpleType:
      description: >-
        Payment's amount
      type: number
      #format: decimal  # fractionDigits=5, totalDigits=18, minInclusive=0
      minimum: 0
      example: 44.44
      x-precision: 2
    ActiveCurrencyCode:
      description: >-
        Payment's currency code
      type: string
      enum:
        - CAD
      x-example: CAD
    CashAccount38:
      description: >-
        Data block that contains debtor/sender account information.
      type: object
      required:
        - identification
      properties:
        identification: # Id
          $ref: '#/components/schemas/AccountIdentification4Choice'
    DebtorContact:
      description: >-
        This block can contain additional contact details for the debtor.
      type: object
      properties:
        mobileNumber: # MobNb
          $ref: '#/components/schemas/DebtorPhoneNumber'
        emailAddress: # EmailAdr
          description: >-
            Optional (usage : example fintech). Debtor's email address
          type: string
          minLength: 1
          maxLength: 2048
          format: email
          example: <EMAIL>
    CreditorContact:
      description: >-
        Optional for ANR et Instant payment<br>
        Not required for Payment money request<br>
        Required for Regular and auto-deposit payment<br>
        Set of elements used to indicate how to contact the party.<br>
        The elements in this block are used to specify the additional creditor/recipient details such as email and mobile phone number.<br>
        Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this block is not required.<br>
        If PaymentTypeInformation.LocalInstrument.Proprietary is ACCOUNT_DEPOSIT_PAYMENT or REALTIME_ACCOUNT_DEPOSIT_PAYMENT then this block is optional.<br>
        In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided.
      type: object
      properties:
        mobileNumber: # MobNb
          $ref: '#/components/schemas/CreditorPhoneNumber'
        emailAddress: # EmailAdr
          description: >-
            For all payment type, except Fulfill money request<br>
            Identifies the creditor's address for electronic mail (e-mail).<br>
            Creditor's/recipient's mobile phone number.<br>
            Conditional: when the ContactDetails block is present, at least one of the elements, mobile phone or email must be specified.
          type: string
          minLength: 1
          maxLength: 2048
          format: email
          example: <EMAIL>
    CreditTransferTransaction39:
      description: >-
        Set of elements providing information specific to the individual credit transfer(s).
      type: object
      required:
        - paymentIdentification
        - paymentTypeInformation
        - interbankSettlementAmount
        - debtor
        - debtorAccount
        - supplementaryData
      properties:
        paymentIdentification: # PmtId
          $ref: '#/components/schemas/PaymentIdentification7'
        paymentTypeInformation: # PmtTpInf
          $ref: '#/components/schemas/PaymentTypeInformation28'
        interbankSettlementAmount: # IntrBkSttlmAmt
          $ref: '#/components/schemas/ActiveCurrencyAndAmount'
        interbankSettlementDate: # IntrBkSttlmDt
          $ref: '#/components/schemas/ISODate'
        debtor: # Dbtr
          $ref: '#/components/schemas/DebtorIdentification'
        debtorAccount: # DbtrAcct
          $ref: '#/components/schemas/CashAccount38'
        creditor: # Cdtr
          $ref: '#/components/schemas/CreditorIdentification'
        creditorAccount: # CdtrAcct
          $ref: '#/components/schemas/CashAccount38'
        remittanceInformation: # RmtInf
          $ref: '#/components/schemas/RemittanceInformation16'
        mandateRelatedInformation:
          $ref: '#/components/schemas/MandateRelatedInformation'
        supplementaryData:
          $ref: '#/components/schemas/SupplementaryData'
    ExternalPaymentTransactionStatus1Code:
      type: string
      enum: ['ACTC', 'PDNG', 'ACSP', 'RJCT', 'CANC', 'RCVD', 'ACPD', 'BLCK']
      description: >-
        This indicates the transaction processing ISO status. Acceptable codes
        ACTC - AcceptedTechnicalValidation
        PDNG - Pending
        ACSP - Accepted Settlement In progress - indicates transaction was processed
        successfully,
        RJCT - indicates transaction processing has been cancelled
        CANC - Cancelled
        RCVD - Received
        ACPD - AcceptedClearingProcessed
        BLCK - Blocked
      x-example: ACSP
    FIToFICustomerCreditTransferV08:
      type: object
      required:
        - groupHeader
        - creditTransferTransactionInformation
      properties:
        groupHeader: # GrpHdr
          $ref: '#/components/schemas/GroupHeader93'
        creditTransferTransactionInformation: # CdtTrfTxInf
          $ref: '#/components/schemas/CreditTransferTransaction39'
    GenericAccountIdentification1:
      type: object
      required:
        - identification
      properties:
        identification: # Id
          description: >-
            The account number.<br>
            Usage: Valid format: aaa-bbbbb-cccccccccccccccccccccccc where<br>
            123 is the Institution Id (fixed length 3 digits)<br>
            bbbbb is the Transit Number (fixed length 5 digits)<br>
            cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          pattern: ^\\d{3}-\\d{5}-\\d{1,24}$
          example: aaa-bbbbb-cccccccccccc
    GenericOrganisationIdentification1:
      type: object
      required:
        - identification
      properties:
        identification: # Id
          description: >-
            Usage: This must contain the identification of the Debtor by their customer id as was set by their FI (BNCID or ID for Fintech )
          type: string
          minLength: 1
          maxLength: 128
    GroupHeader93:
      description: >-
        Set of characteristics shared by all individual transactions included in the message.
      type: object
      properties:
        messageIdentification: # MsgId
          description: >-
            Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message.<br/><br/>
            Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.<br/><br/>
            A new id message for each request.
          type: string
          minLength: 1
          maxLength: 35
          example: d04273e9014645c2b12e3ef18ef8589c
        creationDateTime: # CreDtTm
          description: >-
            Date and time at which the message was created.
          type: string
          format: date-time
          example: "2019-05-05T17:29:12.123Z"
        numberOfTransactions: # NbOfTxs
          description: >-
            Number of individual transactions contained in the message. Usage: Expected value is 1
          type: string
          pattern: '[0-9]{1,15}'
      required:
        - messageIdentification
        - creationDateTime
        - numberOfTransactions
        - settlementInformation
    ISODate:
      type: string
      format: date
      description: >-
        Requested execution date and time for payment request. A particular
        point in the progression of time in a calendar year expressed in the YYYY-MM-DD
        format. This representation is defined in XML Schema Part 2 Datatypes Second
        Edition - W3C Recommendation 28 October 2004 which is aligned with ISO 8601.
        </br></br> Should be filled with today's date. Format: YYYY-MM-DD
      example: '2020-01-23'
    OrganisationIdentification29:
      type: object
      properties:
        other: # Othr
          type: array
          items:
            $ref: '#/components/schemas/GenericOrganisationIdentification1'
      required:
        - other
    Party38Choice:
      type: object
      description: This data block identifies the Debtor by their customer id at their financial institution
      properties:
        organisationIdentification:
          $ref: '#/components/schemas/OrganisationIdentification29'
      required:
        - organisationIdentification
    DebtorIdentification:
      type: object
      description: >-
        The party which owes the money to the (ultimate) creditor. Usage: This data block must identify the debtor/sender
      properties:
        name: # Nm
          type: string
          minLength: 1
          maxLength: 140
          description: >-
            Name by which a party is known and which is usually used to identify that party. Optional (usage : example fintech)
        identification: # Id
          $ref: '#/components/schemas/Party38Choice'
        contactDetails: # CtctDtls
          $ref: '#/components/schemas/DebtorContact'
    CreditorIdentification:
      type: object
      description: >-
        For all payment types, except Fulfill money request<br>
        Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this element is not required. In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided
      properties:
        name: # Nm
          type: string
          minLength: 1
          maxLength: 140
          description: >-
            Name of the creditor/recipient. When present, this will be used in the notifications sent to the Creditor.<br>
            Conditional usage: If PaymentTypeInformation.LocalInstrument.Proprietary is FULFILL_REQUEST_FOR_PAYMENT then this element is not required. In all other cases either the Creditor.Name plus the Creditor.ContactDetails must be provided
        contactDetails: # CtctDtls
          $ref: '#/components/schemas/CreditorContact'
    PaymentAuthentication:
      description: >-
        Required only if paymentType is REGULAR_PAYMENT. </br>
        Data block containing information regarding the authentication that will be required to complete this transfer.
      type: object
      required:
        - securityQuestion
        - securityAnswer
        - hashType
        - hashSalt
      properties:
        securityQuestion:
          description: >-
            Security question text.
          type: string
          minLength: 1
          maxLength: 40
        securityAnswer:
          description: >-
            Security answer encrypted. Answer to the security question (as provided by the customer) with leading and trailing whitespace trimmed, uppercased, postfixed with hashSalt if present,
            hashed using the alghoritm identified by hashType and then Base64 encoded. ISO-8859-1 encoding to be used when the hash is generated.
            This will only be present if the authenticationRequired field (received from the get operation) indicates that validation is required.
          type: string
          minLength: 3
          maxLength: 64
        hashType:
          description: >-
            Alghorithm used to hash the security answer. It has to be one of values supported by the system. Required if authenticationRequired is true.
          type: string
          enum: [ 'SHA2' ]
        hashSalt:
          description: >-
            Salt used to hash the security answer. Required if authenticationRequired is true. Hash salt to strengthen encryption.
          type: string
          minLength: 1
          maxLength: 44
    PaymentIdentification7:
      type: object
      required:
        - endToEndIdentification
      properties:
        instructionIdentification: # InstrId
          description: >-
            The unique business transaction ID.<br/></br>
            Consumer generated transaction identifier.
          type: string
          maxLength: 35
        endToEndIdentification: # EndToEndId
          description: >-
            EndToEndIdentification is a transaction identifier used across BNC systems (end-to-end chain). It is internal to BNC.
          type: string
          maxLength: 35
    PaymentTypeInformation28:
      type: object
      properties:
        localInstrument: # LclInstrm
          $ref: '#/components/schemas/LocalInstrument2Choice'
      required:
        - localInstrument
    DebtorPhoneNumber:
      type: string
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      description: >-
        Optional (usage : example fintech). Debtor's mobile phone number </br>
        The collection of information which identifies a specific phone or FAX number as defined by telecom services.
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).
      minLength: 1
      maxLength: 30
      example: ******-555-1212
    CreditorPhoneNumber:
      type: string
      pattern: \+[0-9]{1,3}-[0-9()+\-]{1,30}
      description: >-
        For all payment type, except Fulfill money request<br>
        Identifies the creditor's mobile phone number.<br>
        Creditor's/recipient's mobile phone number.<br>
        Conditional: when the ContactDetails block is present, at least one of the elements, mobile phone or email must be specified.<br>
        The collection of information which identifies a specific phone or FAX numberas defined by telecom services.<br>
        It consists of a "+" followed by the country code (from 1 to 3 characters)
        then a "-" and finally, any combination of numbers, "(", ")", "+" and "-"
        (up to 30 characters).</br>
      minLength: 1
      maxLength: 30
      example: ******-555-1212
    RemittanceInformation16:
      type: object
      properties:
        unstructured: # Ustrd
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 140
          description: Remittance information in an unstructured form. Up to 3 elements
            allowed for a total of 420 characters. Every part of the remittance information
            must be between 1 to 140 characters long and match the
            regex ^((?!(#|\&|\\|%|\<|\>|http\:|https\:|www|function|return|javascript|select|drop|truncate)).)*$
          maxItems: 3
          x-code-size: 2001
          #structured: # Strd
          #type: array
          #items:
          #$ref: '#/components/schemas/StructuredRemittanceInformation16'
          #description: Remittance information in a structured form. Up to 5 block
          #allowed.
          #maxItems: 5
          #x-code-size: 2002
    SupplementaryData:
      description: >-
        Additional information that cannot be captured in the structured elements and/or any other specific block.
      type: object
      required:
        - fraudSupplementaryInfo
        - clientType
      properties:
        creditorPreferredLanguage:
          description: >-
            Creditor's preferred communication language for notifications
          type: string
          enum:
            - EN
            - FR
          example: EN
        interacMoneyRequestId:
          description: >-
            Only for payment of money request </br>
            Interac Money Request ID
          type: string
          minLength: 1
          maxLength: 35
        accountHolderName:
          description: >-
            The debtor account holder name. If empty, the debtor name will be passed to Interac.
          type: string
          minLength: 1
          maxLength: 80
        creditorAutoDepositRegNumber:
          description: >-
            Creditor Account alias Registration reference number generated by Interac, for using at payment initiation for the Auto deposit Send. It's mandatory if payment type is 'ACCOUNT_ALIAS_PAYMENT' or 'REALTIME_ACCOUNT_ALIAS_PAYMENT'
          type: string
          minLength: 1
          maxLength: 35
        fraudSupplementaryInfo:
          $ref: '#/components/schemas/SupplementaryInfo'
        paymentAuthentication:
          $ref: '#/components/schemas/PaymentAuthentication'
        clientType:
          description: >-
            The client type to describe whether it is an individual or an organization.
          type: string
          enum:
            - INDIVIDUAL
            - ORGANIZATION
          example: INDIVIDUAL
        confirmationId:
          description: >-
            The confimration number, short ID for the transaction.
          type: string
          minLength: 1
          maxLength: 35
          example: "d04273e49"
        creditorId:
          description: >-
            The id of the recipient
          type: string
          format: uuid
          example: 95D2E46D-DC12-4510-BFEA-B5C268A206B2
    MandateRelatedSupplementaryData:
      description: Additional data required for the deferred transactions
      type: object
      required:
        - numberOfRemaining
        - currentOccurrence
        - originalChannelId
        - originalChannelType
      properties:
        numberOfRemaining:
          type: integer
          format: int32
          description: Number of deferred remaining
          minimum: 0
        currentOccurrence:
          type: integer
          format: int32
          description: Current payment number in process
          minimum: 1
        originalChannelType:
          type: string
          enum:
            - WEB
            - MOBILE
            - BATCH
          example: WEB
        originalChannelId:
          type: string
          description: Original Channel Identifier
          minLength: 4
          maxLength: 10
          example: OSFIN
    SupplementaryInfo:
      description: Additional data block required for Fraud or Compliance reasons.
        All these 5 elements are required. Any conditional exceptions must be discussed/established
        with the Interac Fraud team prior to implementation.
      type: object
      required:
        - clientIpAddress
        - clientDeviceFingerPrint
        - clientAuthenticationMethod
      properties:
        clientIpAddress:
          description: Public IP Address used by the Customer during payment initiation.
            This address must match the regex ^[a-fA-F0-9. :]*$
          type: string
          minLength: 2
          maxLength: 64
          pattern: ^[a-fA-F0-9. :]*$
          example: *******
        clientCardNumber:
          description: >-
            Sender's card number associated with the account, HASHED. Supported hash algorithm: SHA256
          type: string
          pattern: '^[\w\p{L}\p{Mn}.,''\-\/ ]*$'
          minLength: 1
          maxLength: 64
          example: ****************
        clientDeviceFingerPrint:
          description: >-
            Unique device fingerprint (the session id). The following options/types are supported in
            this preferred priority. They must start with a prefix (ITM or FTM or
            UID or CID) followed by the value of the ************* session id/device
            id/cookie id
              Interac ThreatMetrix profiling session Id        - ITM*************
              FI ThreatMetrix profiling Session Id             - FTM*************
              Unique Device Identifier of device               - UID*************
              Cookie Id Placed at customers computer or device - CID**************
          type: string
          pattern: '^[\w\p{L}\p{Mn}.,''\-\/ ]*$'
          minLength: 1
          maxLength: 256
          example: ITM1234567890123
        clientAuthenticationMethod:
          description: >-
            Authentication method option used to authenticate the customer (sender)
            prior to payment initiation.
            The following values are currently supported.
          type: string
          enum:
            - PASSWORD
            - PVQ
            - FINGERPRINT
            - BIO_METRICS
            - OTP
            - TOKEN
            - MAKER_CHECKER
            - NONE
            - OTHER
          x-example: PASSWORD
        accountCreationDate:
          description: >-
            Date at which the account was created.
          type: string
          format: date
          example: "2022-01-23"
    GroupHeader:
      type: object
      properties:
        messageIdentification: # MsgId
          description: >-
            The reference number for this request message. It must be unique (within each FI system) for every request.<br/><br/>
            A new id message for each request.
          type: string
          minLength: 1
          maxLength: 36
          example: d04273e9014645c2b12e3ef18ef8589c
        creationDateTime: # CreDtTm
          description: >-
            Date and time at which the message was created.
          type: string
          format: date-time
          example: "2019-05-05T17:29:12.123Z"
    PaymentIdentification:
      type: object
      required:
        - endToEndIdentification
      properties:
        instructionIdentification: # InstrId
          description: >-
            The unique business transaction ID.<br/></br>
            Consumer generated transaction identifier.
          type: string
          maxLength: 35
        endToEndIdentification: # EndToEndId
          description: >-
            EndToEndIdentification is a transaction identifier used across BNC systems (end-to-end chain). It is internal to BNC.
          type: string
          maxLength: 35
    GenericAccountIdentification:
      type: object
      required:
        - identification
      properties:
        identification: # Id
          description: >-
            The account number.<br>
            Usage: Valid format: aaa-bbbbb-cccccccccccccccccccccccc where<br>
            123 is the Institution Id (fixed length 3 digits)<br>
            bbbbb is the Transit Number (fixed length 5 digits)<br>
            cccccccccccccccccccccccc is the bank account number (up to max 24 digits)
          type: string
          minLength: 1
          maxLength: 34
          example: aaa-bbbbb-cccccccccccc
    AccountIdentificationChoice:
      description: >-
        Account identification wrapper element.
      type: object
      required:
        - other
      properties:
        other:
          $ref: '#/components/schemas/GenericAccountIdentification'
    CreditorAccount:
      description: >-
        Data block that contains debtor/sender account information.
      type: object
      required:
        - identification
      properties:
        identification: # Id
          $ref: '#/components/schemas/AccountIdentificationChoice'
    CreditTransferTransaction:
      type: object
      required:
        - messageIdentification
        - creationDateTime
        - paymentIdentification
      properties:
        paymentIdentification: # PmtId
          $ref: '#/components/schemas/PaymentIdentification'
        creditorAccount: # CdtrAcct
          $ref: '#/components/schemas/CreditorAccount'
        cancelReason:
          description: >-
            Cancellation reason in free text format </br>
          type: string
          minLength: 1
          maxLength: 400
    DomesticCancellationPaymentRequest:
      type: object
      required:
        - groupHeader
        - creditTransferTransactionInformation
      properties:
        groupHeader: # GrpHdr
          $ref: '#/components/schemas/GroupHeader'
        creditTransferTransactionInformation: # CdtTrfTxInf
          $ref: '#/components/schemas/CreditTransferTransaction'
    MandateRelatedInformation:
      type: object
      required:
        - mandateIdentification
        - frequencyTypePeriod
        - countPerPeriod
        - supplementaryData
      properties:
        mandateIdentification:
          type: string
          description: Unique deferred identifier
          minLength: 1
          maxLength: 36
        frequencyTypePeriod:
          type: string
          description: >-
            Period of the recurring transfer.
            DAIL = Daily - Event takes place every day.
            MNTH = Monthly - Event takes place every month or once a month.
            WEEK = Weekly - Event takes place once a week.
            TWMN = TwiceAMonth - Event takes place two times a month.
            TOWK = EveryTwoWeeks - Event takes place every two weeks.
            ONCE = Once - Event takes place once.
            EOFM = End Of Month - Event takes place on the last day of every months.
            Enum: [ MNTH, WEEK, DAIL, TWMN, TOWK, ONCE, EOFM ]
          enum:
            - MNTH
            - WEEK
            - DAIL
            - TWMN
            - TOWK
            - ONCE
            - EOFM
        countPerPeriod:
          type: integer
          format: int32
          description: >-
            Number of payments total
            Example : 1 for a single deferred and more than 1 for a series of deferred
          minimum: 1
          example: 17
        supplementaryData:
          $ref: '#/components/schemas/MandateRelatedSupplementaryData'
    DomesticPaymentNotificationRequest:
      type: object
      required:
        - endToEndIdentification
      properties:
        endToEndIdentification:
          type: string
          description: EndToEndIdentification is a transaction identifier used across BNC systems (end-to-end chain). It is internal to BNC (does not represent Interac Payment Reference Number that is used as a parameter in the URI).
          maxLength: 36
    DomesticPaymentAuthenticationRequest:
      type: object
      required:
        - fIToFICustomerCreditTransfer
      properties:
        fIToFICustomerCreditTransfer:
          $ref: '#/components/schemas/FIToFICustomerCreditTransfer'
    FIToFICustomerCreditTransfer:
      type: object
      required:
        - creditTransferTransactionInformation
      properties:
        creditTransferTransactionInformation:
          type: object
          required:
            - paymentIdentification
            - supplementaryData
          description: Information specific to the individual credit transfer.
          properties:
            paymentIdentification:
              $ref: '#/components/schemas/PaymentIdentification'
            supplementaryData:
              description: >-
                Additional information that cannot be captured in the structured elements and/or any other specific block.
              type: object
              required:
                - paymentAuthentication
              properties:
                paymentAuthentication:
                  $ref: '#/components/schemas/PaymentAuthentication'
    AgentId:
      description: >-
        The id for the on behalf transaction.
      type: string
      example: 'ABCD123'