openapi: 3.0.3
info:
  title: Payment Callback API
  x-version-history:
    '0.4': 2024-02-05 - Change base path url
    '0.3': 2023-10-23 - Renamed the API contract
    '0.2': 2023-10-06 - Message bodies for operation if present are mandatory
    '0.1': 2023-06-09 - Created(for OS)
  description: Callback for domestic etransfer call.
  version: 1.2.0
  termsOfService: 'https://www.bnc.ca/'
  contact:
    name: Support PAYPRO
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
servers:
  - url: https://pmt.apis.bnc.ca/
tags:
  - name:  Payment Callback
    description: Callback for Transfer Payment API
paths:
  /callbacks/payments/{endToEndIdentification}:
    parameters:
      - $ref: "#/components/parameters/EndToEndIdentification"
      - $ref: "#/components/parameters/Version"
      - $ref: "#/components/parameters/RequestId"
    put:
      tags:
        - Domestic Payment Callback
      description: >-
        This service is used by payment domain to notify its customers of payment status. This endpoint must be implemented by these customers so we can send our REST calls to them.
      operationId: callbackTransfer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CallbackSendRequest'
      responses:
        '204': { "$ref": "#/components/responses/204-no-content" }
        '400': { "$ref": "#/components/responses/400-bad-request" }
        '404': { "$ref": "#/components/responses/404-not-found" }
        '500': { "$ref": "#/components/responses/500-internal-server-error" }
        '503': { "$ref": "#/components/responses/503-service-unavailable" }
components:
  parameters:
    CorrelationId:
      in: header
      name: x-correlation-id
      description: >-
        Unique ID generated for each request used for message tracking purposes.
        Technical and unique traceability identifier. Used by monitoring and log tolls such as Datadog and Splunk.
      schema:
        type: string
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
    EndToEndIdentification:
      name: endToEndIdentification
      in: path
      required: true
      description: >-
        EndToEndIdentification is a transaction identifier used across BNC systems (end-to-end chain). It is internal to BNC (does not represent Interac Payment Reference Number that is used as a parameter in the URI).
      schema:
        type: string
    RequestId:
      in: header
      name: x-request-id
      description: >-
        Unique ID generated for each request used for message tracking purposes.
        Technical and unique traceability identifier. Used by monitoring and log tolls such as Datadog and Splunk.
      schema:
        type: string
      required: true
      example: 3bcdf7fa-7e48-4565-9751-d8acbdb64d8b
    VersionAncien:
      in: header
      name: x-version
      description: The version of the API.
      schema:
        type: string
      required: true
      example: V1
    Version:
      in: header
      name: Accept-version
      description: The version of the API.
      schema:
        type: string
      required: true
      example: v1
  responses:
    204-no-content:
      description: OK - No Content
    400-bad-request:
      description: Bad Request - Validation Errors
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Errors"
    404-not-found:
      description: Resources Not Found
    500-internal-server-error:
      description: Internal Server Error
    503-service-unavailable:
      description: Service Unavailable
  schemas:
    CallbackSendRequest:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          description: Status of the request
          enum:
            - SUCCESS
            - ERROR
          example: SUCCESS
        errors:
          type: array
          description: If status is 'ERROR', specifies the reason.
          items:
            $ref: '#/components/schemas/Error'
    Error:
      description: Error code.
      type: object
      title: Error
      required:
        - code
        - text
        - origin
      properties:
        code:
          description: Error Source Code.
          title: Code
          type: string
          maxLength: 50
          example: "ETRANSFER_NOT_EXIST"
        text:
          description: Description.
          title: Text
          type: string
          maxLength: 2000
          example: "Error description"
        origin:
          description: Error source.
          title: Origin
          type: string
          maxLength: 55
          example: "pmt-domestic-interac-etransfer-api"
        rule:
          description: Business rule.
          title: Rule
          type: string
          maxLength: 50
          example: "R_IEC_AUT_001"
    Errors:
      type: object
      description: Returns a list of errors.
      properties:
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
 