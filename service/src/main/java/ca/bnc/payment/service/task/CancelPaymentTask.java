package ca.bnc.payment.service.task;

import ca.bnc.payment.handler.fallback.cancelpayment.CancelPaymentFallbackHandler;
import ca.bnc.payment.handler.happypath.cancelpayment.CancelPaymentHandler;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CancelPaymentTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CancelPaymentTask.class);

    private final CancelPaymentHandler cancelPaymentHandler;
    private final CancelPaymentFallbackHandler cancelPaymentFallbackHandler;
    private final LoggingFacade loggingFacade;
    private final LogContextHolder logContextHolder;

    @Async("workerThreadExecutor")
    public void execute(final CancelPaymentContext cancelPaymentContext) {

        logContextHolder.runWithContext(
                () -> doExecute(cancelPaymentContext),
                cancelPaymentContext.getTaskContext().getInitalLogContext());

    }

    private void doExecute(final CancelPaymentContext cancelPaymentContext) {

        try {
            cancelPaymentHandler.handleCancelPayment(cancelPaymentContext);
        } catch (Exception exception) {
            loggingFacade.error(LOGGER, exception);
            if (!cancelPaymentContext.isCancelPaymentCommitted()) {
                cancelPaymentFallbackHandler.handleCancelPaymentFallback(exception, cancelPaymentContext);
            }
        }

    }

}
