package ca.bnc.payment.service.interac;

import ca.bnc.payment.exception.validation.IdentifierNotFoundException;
import ca.bnc.payment.facade.PartyFacade;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.repository.OutgoingPaymentRepository;
import ca.nbc.payment.pmtpartnersparty.model.Identifier;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static ca.bnc.payment.constant.PartyErrorMessage.INTERAC_ID_NOT_FOUND;
import static ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus.ACTIVE;

@Service
@RequiredArgsConstructor
public final class InteracIdentifierService {
    private final PartyFacade partyFacade;
    private final OutgoingPaymentRepository outgoingPaymentRepository;

    public Identifier findActiveIdentifier(final RequestDto<SendPaymentRequest> requestDto) {
        final Identifiers identifiers = partyFacade.getPartyClientById(requestDto);
        return identifiers.getIdentifiers()
                .stream()
                .filter(identifier -> identifier.getStatus() == ACTIVE)
                .findFirst()
                .orElseThrow(() -> deleteAndThrowIdentifierNotFoundException(requestDto));
    }

    private IdentifierNotFoundException deleteAndThrowIdentifierNotFoundException(
            final RequestDto<SendPaymentRequest> requestDto) {
        outgoingPaymentRepository.delete(requestDto.getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification());
        return new IdentifierNotFoundException(INTERAC_ID_NOT_FOUND.formatted(requestDto.getXClientId()));
    }
}
