package ca.bnc.payment.service;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.repository.RepositoryException;
import ca.bnc.payment.exception.validation.ExistingPaymentException;
import ca.bnc.payment.facade.PaymentNotificationKafkaFacade;
import ca.bnc.payment.facade.PptdKafkaFacade;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public final class SendNotificationService {

    private final PptdKafkaFacade pptdKafkaFacade;
    private final PaymentNotificationKafkaFacade paymentNotificationKafkaFacade;

    public void handleNotification(final RequestDto<SendPaymentRequest> requestDto,
                                   final OutgoingDomesticPayment outgoingDomesticPayment,
                                   final String interactUserId,
                                   final OutgoingPaymentException outgoingPaymentException) {

        if (isNotRepositoryOrExistingException(outgoingPaymentException)) {
            pptdKafkaFacade.sendPptdKafkaMessage(requestDto, outgoingDomesticPayment.getDebtorInteracId(), outgoingPaymentException);
        }
        paymentNotificationKafkaFacade.sendPaymentNotificationKafkaMessage(
                requestDto, outgoingPaymentException, interactUserId, null);
    }

    public void handleNotification(final SendPaymentContext sendPaymentContext) {
        pptdKafkaFacade.sendPptdKafkaMessage(sendPaymentContext);
        paymentNotificationKafkaFacade.sendPaymentNotificationKafkaMessage(sendPaymentContext);
    }

    private boolean isNotRepositoryOrExistingException(final OutgoingPaymentException outgoingPaymentException) {
        return !(outgoingPaymentException instanceof RepositoryException || outgoingPaymentException instanceof ExistingPaymentException);
    }
}
