package ca.bnc.payment.config;

import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@EnableConfigurationProperties({KafkaConfigProperties.class, KafkaRetryTemplateProperties.class,})
public class KafkaConfig {
    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaConfig.class);
    private static final String SCRAM_LOGIN_MODULE = "org.apache.kafka.common.security.scram.ScramLoginModule";
    private static final String PLAIN_LOGIN_MODULE = "org.apache.kafka.common.security.plain.PlainLoginModule";
    public static final String SASL_SSL = "sasl_ssl";

    private record LoginModule(String name, String value) {
    }


    private static final Map<String, LoginModule> LOGIN_MODULE_MAP = Map.of(
            "scram-sha-512", new LoginModule("Scram", SCRAM_LOGIN_MODULE),
            "plain", new LoginModule("Plain", PLAIN_LOGIN_MODULE)
    );


    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate(
            final KafkaConfigProperties kafkaConfigProperties,
            final LoggingFacade loggingFacade,
            final ObjectMapper objectMapper) {
        Map<String, Object> configProps = buildCommonKafkaConfigs(kafkaConfigProperties);

        if (isSimpleAuthenticationSecurityLayer(kafkaConfigProperties.securityProtocol())) {
            addJassConfig(kafkaConfigProperties, loggingFacade, configProps);
        }

        loggingFacade.info(LOGGER, "Kafka BOOTSTRAP_SERVERS: %s".formatted(kafkaConfigProperties.bootstrapServers()));

        final ProducerFactory<String, Object> producerFactory =
                new DefaultKafkaProducerFactory<>(configProps, new StringSerializer(), new JsonSerializer<>(objectMapper));

        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public RetryTemplate kafkaRetryTemplate(final KafkaRetryTemplateProperties retryTemplateProperties) {
        RetryTemplate retryTemplate = new RetryTemplate();
        FixedBackOffPolicy fixedBackOffPolicy = new FixedBackOffPolicy();
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(retryTemplateProperties.maxAttempts(), Map.of(Exception.class, true));
        fixedBackOffPolicy.setBackOffPeriod(retryTemplateProperties.backoffPeriod());
        retryTemplate.setBackOffPolicy(fixedBackOffPolicy);
        retryTemplate.setRetryPolicy(retryPolicy);

        return retryTemplate;

    }

    private static Map<String, Object> buildCommonKafkaConfigs(KafkaConfigProperties kafkaConfigProperties) {
        Map<String, Object> configProps = new HashMap<>();

        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfigProperties.bootstrapServers());
        configProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, kafkaConfigProperties.securityProtocol());
        configProps.put(SaslConfigs.SASL_MECHANISM, kafkaConfigProperties.saslMechanism());
        return configProps;
    }

    private boolean isSimpleAuthenticationSecurityLayer(final String securityProtocol) {
        return SASL_SSL.equalsIgnoreCase(securityProtocol);
    }

    private void addJassConfig(final KafkaConfigProperties kafkaConfigProperties,
                               final LoggingFacade loggingFacade,
                               final Map<String, Object> configProps) {

        // Configure as per
        // https://docs.confluent.io/platform/current/security/authentication/sasl/plain/overview.html#jaas
        Optional.ofNullable(kafkaConfigProperties.saslMechanism())
                .map(String::toLowerCase)
                .map(LOGIN_MODULE_MAP::get)
                .ifPresent(loginModule -> addJaasConfig(kafkaConfigProperties, loggingFacade, configProps, loginModule));
    }

    private void addJaasConfig(
            final KafkaConfigProperties kafkaConfigProperties,
            final LoggingFacade loggingFacade,
            Map<String, Object> configProps,
            final LoginModule loginModule) {

        final String jaasConfig = "%s required username='%s' password='%s';".formatted(
                loginModule.value,
                escapeQuotes(kafkaConfigProperties.username()),
                escapeQuotes(kafkaConfigProperties.password()));
        configProps.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
        loggingFacade.info(
                LOGGER,
                "Configure KafkaTemplate with %s login module - %s".formatted(loginModule.name, loginModule.value));

    }

    private String escapeQuotes(final String value) {
        return value.replace("'", "\\'");
    }

}

