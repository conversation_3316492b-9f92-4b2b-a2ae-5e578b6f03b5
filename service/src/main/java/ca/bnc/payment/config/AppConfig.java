package ca.bnc.payment.config;

import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.normalization.lib.service.impl.NormalizationServiceImpl;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.datatype.jsr310.ser.OffsetDateTimeSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.time.Clock;
import java.time.format.DateTimeFormatter;

@Configuration
public class AppConfig {

    public static final String CALL_BACK_CLIENT_CACHE = "callbackClientCache";

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> builder.serializers(new OffsetDateTimeSerializer(
                OffsetDateTimeSerializer.INSTANCE,
                false,
                FORMATTER,
                JsonFormat.Shape.STRING
        ));
    }

    @Bean
    @Profile("!component-test")
    public Clock clock() {
        return Clock.systemUTC();
    }

    @Bean
    public NormalizationService normalizationService() {
        return new NormalizationServiceImpl();
    }

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(CALL_BACK_CLIENT_CACHE);
    }
}
