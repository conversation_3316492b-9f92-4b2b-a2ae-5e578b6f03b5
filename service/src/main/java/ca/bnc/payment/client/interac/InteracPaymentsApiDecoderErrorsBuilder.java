package ca.bnc.payment.client.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ErrorModel;
import ca.bnc.payment.mapper.interac.InteracErrorsMapper;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;

import static ca.bnc.payment.exception.OutgoingPaymentException.Error;

@Component
@AllArgsConstructor
public class InteracPaymentsApiDecoderErrorsBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(InteracPaymentsApiDecoderErrorsBuilder.class.getName());

    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;
    private final InteracErrorsMapper interacErrorsMapper;

    public Error buildErrorFromResponseBody(final Response response) {
        Error error = null;
        try (InputStream inputStream = response.body().asInputStream()) {
            final ErrorModel interacError = objectMapper.readValue(inputStream, ErrorModel.class);
            error = interacErrorsMapper.mapInteracErrorModelToError(interacError);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from the Interac Payments API", exception);
        }

        return error;
    }

}
