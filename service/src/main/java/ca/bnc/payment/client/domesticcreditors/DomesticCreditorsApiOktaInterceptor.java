package ca.bnc.payment.client.domesticcreditors;

import ca.bnc.payment.client.interceptor.InterceptorFactory;
import feign.RequestInterceptor;
import feign.RequestTemplate;


public final class DomesticCreditorsApiOktaInterceptor implements RequestInterceptor {

    private static final String TOKEN_CONFIG_NAME = "domesticCreditorsScope";
    private final RequestInterceptor delegate;


    public DomesticCreditorsApiOktaInterceptor(final InterceptorFactory interceptorFactory) {
        this.delegate = interceptorFactory.createBncInterceptor(TOKEN_CONFIG_NAME);
    }

    @Override
    public void apply(final RequestTemplate template) {
        delegate.apply(template);
    }

}
