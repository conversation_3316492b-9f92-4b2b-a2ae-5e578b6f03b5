package ca.bnc.payment.client.fraud;

import ca.bnc.payment.pmt_outgoing_payment_management_fraud_resources.generated.model.Problem;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.InputStream;

@Component
@AllArgsConstructor
public class FraudDecoderErrorsBuilder {

    private static final Logger LOGGER = LoggerFactory.getLogger(FraudDecoderErrorsBuilder.class.getName());

    private final ObjectMapper objectMapper;
    private final LoggingFacade loggingFacade;

    public String extractTextFromError(final Response response) {
        try (InputStream inputStream = response.body().asInputStream()) {
            final Problem fraudError = objectMapper.readValue(inputStream, Problem.class);
            return fraudError.getDetail();
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, "An error occurred while parsing error response from the Online Fraud API", exception);
        }

        return null;
    }
}
