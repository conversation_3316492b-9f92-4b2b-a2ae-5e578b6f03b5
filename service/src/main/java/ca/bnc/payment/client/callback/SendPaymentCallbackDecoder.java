package ca.bnc.payment.client.callback;

import ca.bnc.payment.exception.callback.CallbackException;
import ca.bnc.payment.exception.callback.RetryableCallbackException;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;

import static ca.bnc.payment.constant.CallbackMessage.CALLBACK_BAD_REQUEST;
import static ca.bnc.payment.constant.CallbackMessage.CALLBACK_GENERAL_EXCEPTION;
import static ca.bnc.payment.constant.CallbackMessage.CALLBACK_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.constant.CallbackMessage.CALLBACK_NOT_FOUND;
import static ca.bnc.payment.constant.CallbackMessage.CALLBACK_SERVICE_UNAVAILABLE;
import static software.amazon.awssdk.http.HttpStatusCode.BAD_REQUEST;
import static software.amazon.awssdk.http.HttpStatusCode.INTERNAL_SERVER_ERROR;
import static software.amazon.awssdk.http.HttpStatusCode.NOT_FOUND;
import static software.amazon.awssdk.http.HttpStatusCode.SERVICE_UNAVAILABLE;

@RequiredArgsConstructor
public class SendPaymentCallbackDecoder implements ErrorDecoder {

    private final String callbackUrl;

    @Override
    public Exception decode(final String methodKey, final Response response) {
        int status = response.status();

        return switch (status) {
            case BAD_REQUEST -> new CallbackException(String.format(CALLBACK_BAD_REQUEST, callbackUrl));
            case NOT_FOUND -> new CallbackException(String.format(CALLBACK_NOT_FOUND, callbackUrl));
            case INTERNAL_SERVER_ERROR -> new CallbackException(String.format(CALLBACK_INTERNAL_SERVER_ERROR, callbackUrl));
            case SERVICE_UNAVAILABLE -> new RetryableCallbackException(String.format(CALLBACK_SERVICE_UNAVAILABLE, callbackUrl));
            default -> new CallbackException(String.format(CALLBACK_GENERAL_EXCEPTION, response.status()));
        };
    }
}
