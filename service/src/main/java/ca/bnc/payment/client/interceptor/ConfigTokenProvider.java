package ca.bnc.payment.client.interceptor;

import ca.bnc.payment.exception.okta.OktaException;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.utils.OktaUtil;

import java.util.Objects;

import static ca.bnc.payment.constant.Constant.ERROR_TEXT;

public record ConfigTokenProvider(OktaClientTokenManager oktaClientTokenManager,
                                  String tokenConfigName) implements TokenProvider {
    @Override
    public String getToken() {
        String oktaToken = OktaUtil.getOktaToken(oktaClientTokenManager, tokenConfigName);
        if (Objects.nonNull(oktaToken)) {
            return oktaToken;
        }
        throw new OktaException(ERROR_TEXT);
    }
}
