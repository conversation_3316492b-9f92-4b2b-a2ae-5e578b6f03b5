package ca.bnc.payment.facade;

import ca.bnc.payment.adapter.InteracPaymentsAdapter;
import ca.bnc.payment.lib.interacsecurity.InteracTokenManager;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.AccountAliasType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.CancelPaymentTransaction;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ChannelIndicator;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentOptionResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPostRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPutRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ProductCode;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ReversePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SendTransferNotice;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SignatureType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentResponse;
import ca.bnc.payment.mapper.interac.InteracChannelIndicatorMapper;
import ca.bnc.payment.mapper.interac.InteracInitiatePaymentRequestMapper;
import ca.bnc.payment.mapper.interac.InteracPaymentsCancelTransactionPostRequestModelMapper;
import ca.bnc.payment.mapper.interac.InteracPaymentsCancelTransactionPutRequestModelMapper;
import ca.bnc.payment.mapper.interac.InteracSubmitPaymentRequestMapper;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.bnc.payment.model.task.ReissuePaymentContext;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticPaymentNotificationRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import ca.bnc.payment.util.IdGenerator;
import ca.bnc.payment.util.InteracUtil;
import ca.bnc.payment.util.RequestDtoUtil;
import ca.bnc.payment.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmtpartnersparty.model.Identifier;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.util.NoSuchElementException;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class InteracPaymentsFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(InteracPaymentsFacade.class.getName());

    private final LoggingFacade loggingFacade;
    private final InteracTokenManager interacTokenManager;
    private final InteracPaymentsAdapter interacPaymentsAdapter;
    private final InteracChannelIndicatorMapper interacChannelIndicatorMapper;
    private final InteracInitiatePaymentRequestMapper interacInitiatePaymentRequestMapper;
    private final InteracSubmitPaymentRequestMapper interacSubmitPaymentRequestMapper;
    private final InteracPaymentsCancelTransactionPostRequestModelMapper interacPaymentsCancelTransactionPostRequestModelMapper;
    private final ObjectMapper objectMapper;
    private final InteracPaymentsCancelTransactionPutRequestModelMapper interacPaymentsCancelTransactionPutRequestModelMapper;
    private final RequestDtoUtil requestDtoUtil;
    private final TimeService timeService;
    private final InteracUtil interacUtil;
    private final IdGenerator idGenerator;

    public InitiatePaymentResponse initiatePayment(final SendPaymentContext sendPaymentContext) {
        final TaskContext<SendPaymentRequest> taskContext = sendPaymentContext.getTaskContext();
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final Identifier activeIdentifier = taskContext.getActiveIdentifier();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final InitiatePaymentRequest request = interacInitiatePaymentRequestMapper.map(taskContext.getRequestBody(), activeIdentifier);
        final String accessToken = getAccessToken(request, transactionTime);
        final SupplementaryData.ClientTypeEnum clientType = requestDtoUtil.getClientType(taskContext.getRequestDto());

        return interacPaymentsAdapter.initiatePayment(
                interacUtil.getPaymentParticipantId(),
                activeIdentifier.getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                taskContext.getRetryIndicator(),
                interacUtil.getIndirectConnectorIdForHeader(clientType),
                null,
                request
        );
    }

    public ReversePaymentResponse reversePayment(final SendPaymentContext sendPaymentContext) {
        final TaskContext<SendPaymentRequest> taskContext = sendPaymentContext.getTaskContext();
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final String participantTransactionId = requestDtoUtil.getSendEndToEndIdentification(taskContext.getRequestDto());
        final String accessToken = getAccessToken(null, transactionTime);

        return interacPaymentsAdapter.reversePayment(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                participantTransactionId,
                interacUtil.getPaymentIndirectConnectorId(),
                null
        );
    }

    public SubmitPaymentResponse submitPayment(final SendPaymentContext sendPaymentContext) {
        final TaskContext<SendPaymentRequest> taskContext = sendPaymentContext.getTaskContext();
        final String paymentTransactionToken = sendPaymentContext.getInitiateInteracPaymentResponsePaymentTransactionToken();
        final String clearingSystemReference = sendPaymentContext.getInitiateInteracPaymentResponseETransferId();
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final SubmitPaymentRequest request = interacSubmitPaymentRequestMapper.map(taskContext.getRequestBody(), clearingSystemReference);
        final String accessToken = getAccessToken(request, transactionTime);

        return interacPaymentsAdapter.submitPayment(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                taskContext.getRetryIndicator(),
                paymentTransactionToken,
                interacUtil.getPaymentIndirectConnectorId(),
                null,
                request
        );
    }

    public CancelPaymentTransaction transferCancelBegin(final CancelPaymentContext cancelPaymentContext) {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final String endToEndIdentification = requestDtoUtil.getCancelEndToEndIdentification(taskContext.getRequestDto());
        final String cancellationReason = requestDtoUtil.getCancellationReason(taskContext.getRequestDto());
        final PaymentsCancelTransactionPostRequestModel paymentsCancelTransactionPostRequestModel =
                interacPaymentsCancelTransactionPostRequestModelMapper.map(endToEndIdentification, cancellationReason);
        final String accessToken = getAccessToken(paymentsCancelTransactionPostRequestModel, transactionTime);
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();

        CancelPaymentTransaction cancelPaymentTransaction = interacPaymentsAdapter.transferCancelBegin(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                etransferId,
                interacUtil.getPaymentIndirectConnectorId(),
                null,
                paymentsCancelTransactionPostRequestModel
        );
        cancelPaymentContext.setCancelTxnId(cancelPaymentTransaction.getCancelTxnId());

        return cancelPaymentTransaction;
    }

    public void transferCancelRollback(final CancelPaymentContext cancelPaymentContext) {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final String accessToken = getAccessToken(null, transactionTime);

        interacPaymentsAdapter.transferCancelRollback(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                etransferId,
                cancelPaymentContext.getCancelTxnId(),
                interacUtil.getPaymentIndirectConnectorId(),
                null
        );
    }

    public void transferCancelCommit(final CancelPaymentContext cancelPaymentContext) {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final OffsetDateTime creationDateTime = taskContext.getRequestDto().getRequestBody().getGroupHeader().getCreationDateTime();
        final String txnId = idGenerator.generateInstructionIdentification();
        final PaymentsCancelTransactionPutRequestModel paymentsCancelTransactionPutRequestModel =
                interacPaymentsCancelTransactionPutRequestModelMapper.map(txnId, creationDateTime);
        final String apiSignature = getAccessToken(paymentsCancelTransactionPutRequestModel, transactionTime);
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();

        interacPaymentsAdapter.transferCancelCommit(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                apiSignature,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                etransferId,
                cancelPaymentContext.getCancelTxnId(),
                taskContext.getRetryIndicator(),
                interacUtil.getPaymentIndirectConnectorId(),
                null,
                paymentsCancelTransactionPutRequestModel);
    }

    public void reissuePaymentNotification(final ReissuePaymentContext reissuePaymentContext) {
        final TaskContext<DomesticPaymentNotificationRequest> taskContext = reissuePaymentContext.getTaskContext();
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(taskContext.getChannelType());
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final SendTransferNotice sendTransferNotice = new SendTransferNotice();
        final String accessToken = getAccessToken(sendTransferNotice, transactionTime);

        interacPaymentsAdapter.reissuePaymentNotification(
                interacUtil.getPaymentParticipantId(),
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                etransferId,
                interacUtil.getPaymentIndirectConnectorId(),
                null,
                sendTransferNotice);
    }

    public PaymentOptionResponse getPaymentOptions(final RequestDto<SendPaymentRequest> requestDto, final Identifier activeIdentifier) {
        final OffsetDateTime transactionTime = timeService.getNowOffsetDateTime();
        final ChannelIndicator channelIndicator = interacChannelIndicatorMapper.map(requestDto.getXChannelType().getValue());
        final String accessToken = getAccessToken(null, transactionTime);
        final ProductCode productCode = ProductCode.DOMESTIC;
        final Optional<CreditorContact> creditorContactOptional = Optional.of(requestDto)
                .map(RequestDto::getRequestBody)
                .map(SendPaymentRequest::getFiToFICustomerCreditTransfer)
                .map(FIToFICustomerCreditTransferV08::getCreditTransferTransactionInformation)
                .map(CreditTransferTransaction39::getCreditor)
                .map(CreditorIdentification::getContactDetails);
        final AccountAliasType depositType = creditorContactOptional
                .map(CreditorContact::getMobileNumber)
                .map(mobileNumber -> AccountAliasType.PHONE)
                .orElse(AccountAliasType.EMAIL);
        final String depositHandle = creditorContactOptional
                .map(creditorContact -> Optional.ofNullable(creditorContact.getMobileNumber())
                        .orElseGet(creditorContact::getEmailAddress))
                .orElseThrow(() -> new NoSuchElementException("Neither mobile number nor email address is provided"));

        return interacPaymentsAdapter.getPaymentOptions(
                interacUtil.getPaymentParticipantId(),
                activeIdentifier.getValue(),
                requestDto.getXRequestId().toString(),
                channelIndicator,
                accessToken,
                SignatureType.PAYLOAD_DIGEST_SHA256,
                transactionTime,
                productCode,
                depositType,
                depositHandle,
                interacUtil.getPaymentIndirectConnectorId(),
                null);
    }

    private <T> String getAccessToken(final T request, final OffsetDateTime transactionTime) {
        try {
            final String payload = objectMapper.writeValueAsString(request);
            return Optional
                    .ofNullable(interacTokenManager.getAccessToken(payload, transactionTime))
                    .orElse(StringUtils.SPACE);
        } catch (final Exception exception) {
            loggingFacade.error(LOGGER, exception.getMessage());
            return StringUtils.SPACE;
        }
    }

}
