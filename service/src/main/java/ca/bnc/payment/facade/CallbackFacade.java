package ca.bnc.payment.facade;

import ca.bnc.payment.adapter.CallbackAdapter;
import ca.bnc.payment.client.callback.SendPaymentCallbackClient;
import ca.bnc.payment.mapper.callback.CallbackSendRequestMapper;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.CallbackSendRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.Errors;
import ca.bnc.payment.service.provider.CallbackClientProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CallbackFacade {

    private static final String ACCEPT_VERSION = "v1";

    private final CallbackAdapter callbackAdapter;
    private final CallbackClientProvider callbackClientProvider;
    private final CallbackSendRequestMapper callbackSendRequestMapper;

    public void sendPaymentCallback(final RequestDto<?> requestDto,
                                    final String callbackBaseUrl,
                                    final String endToEndIdentification,
                                    final Errors errors) {

        final SendPaymentCallbackClient callbackClient = callbackClientProvider.createCallbackClient(callbackBaseUrl);
        final CallbackSendRequest request = callbackSendRequestMapper.map(errors);

        callbackAdapter.sendPaymentCallback(
                callbackClient,
                endToEndIdentification,
                ACCEPT_VERSION,
                requestDto.getXRequestId().toString(),
                request);
    }
}
