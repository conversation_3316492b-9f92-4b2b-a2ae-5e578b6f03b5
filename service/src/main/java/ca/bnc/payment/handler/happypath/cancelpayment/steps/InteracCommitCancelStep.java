package ca.bnc.payment.handler.happypath.cancelpayment.steps;

import ca.bnc.payment.exception.interac.InteracException;
import ca.bnc.payment.facade.InteracPaymentsFacade;
import ca.bnc.payment.handler.happypath.cancelpayment.CancelPaymentStep;
import ca.bnc.payment.model.task.CancelPaymentContext;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.CancelStepOrderValues.CANCEL_ORDER_FORTY;
import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_COMMITTED_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_COMMITTING_STATUS;

@Component
@RequiredArgsConstructor
@Order(CANCEL_ORDER_FORTY)
public class InteracCommitCancelStep implements CancelPaymentStep {

    private final InteracPaymentsFacade interacPaymentsFacade;

    @Override
    public void apply(final CancelPaymentContext cancelPaymentContext) {

        cancelPaymentContext.getTaskContext().setProcessingStatus(CANCEL_PAYMENT_COMMITTING_STATUS);
        callCancelCommit(cancelPaymentContext);

    }

    private void callCancelCommit(final CancelPaymentContext cancelPaymentContext) {
        try {
            interacPaymentsFacade.transferCancelCommit(cancelPaymentContext);
            cancelPaymentContext.setCancelPaymentCommitted(true);
            cancelPaymentContext.getTaskContext().setProcessingStatus(CANCEL_PAYMENT_COMMITTED_STATUS);
        } catch (InteracException e) {
            cancelPaymentContext.setDoReverseBankAccountCredit(true);
            cancelPaymentContext.setDoCancelPaymentRollback(true);
            cancelPaymentContext.setDoSetPersistReversedStatus(true);
            throw e;
        }
    }
}
