package ca.bnc.payment.handler.happypath.sendpayment.steps;

import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorException;
import ca.bnc.payment.facade.BankAccountConnectorFacade;
import ca.bnc.payment.handler.happypath.sendpayment.SendPaymentStep;
import ca.bnc.payment.handler.happypath.sendpayment.steps.intermediate.StoreSendPaymentRequest;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.PaymentStatus.SEND_PAYMENT_ACCOUNT_DEBIT_INITIATED_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS;
import static ca.bnc.payment.constant.SendStepOrderValues.SEND_ORDER_TEN;

@Component
@RequiredArgsConstructor
@Order(SEND_ORDER_TEN)
public class BacInitiateDebitStep implements SendPaymentStep {

    private final StoreSendPaymentRequest storeSendPaymentRequest;
    private final BankAccountConnectorFacade facade;

    @Override
    public void apply(final SendPaymentContext sendPaymentContext) {
        sendPaymentContext.getTaskContext().setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        storeSendPaymentRequest.apply(sendPaymentContext, SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        callInitiateDebit(sendPaymentContext);
    }

    private void callInitiateDebit(final SendPaymentContext sendPaymentContext) {
        try {
            final DebitResponse debitResponse = facade.initiateDebit(sendPaymentContext);
            enrichSendPaymentContext(sendPaymentContext, debitResponse);
            sendPaymentContext.getTaskContext().setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_INITIATED_STATUS);
        } catch (BankAccountConnectorException e) {
            sendPaymentContext.setDoPersistReverseStatus(true);
            sendPaymentContext.setOutgoingPaymentException(e);
            throw e;
        }
    }

    private void enrichSendPaymentContext(final SendPaymentContext sendPaymentContext, final DebitResponse debitResponse) {
        sendPaymentContext.setInitiateBankAccountDebitResponseOriginalGroupInfo(
                debitResponse
                        .getFiToFiPaymentStatusReport()
                        .getTransactionInformationAndStatus()
                        .get(0)
                        .getOriginalGroupInformation());
    }
}
