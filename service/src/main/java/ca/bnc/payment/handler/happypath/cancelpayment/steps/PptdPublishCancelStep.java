package ca.bnc.payment.handler.happypath.cancelpayment.steps;

import ca.bnc.payment.facade.PptdKafkaFacade;
import ca.bnc.payment.handler.happypath.cancelpayment.CancelPaymentStep;
import ca.bnc.payment.model.task.CancelPaymentContext;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.CancelStepOrderValues.CANCEL_ORDER_FIFTY;

@Component
@Order(CANCEL_ORDER_FIFTY)
@RequiredArgsConstructor
public class PptdPublishCancelStep implements CancelPaymentStep {

    private final PptdKafkaFacade pptdKafkaFacade;

    @Override
    public void apply(final CancelPaymentContext cancelPaymentContext) {
        if (cancelPaymentContext.isCancelPaymentCommitted()) {
            pptdKafkaFacade.sendPptdKafkaMessage(cancelPaymentContext.getTaskContext());
        }
    }
}
