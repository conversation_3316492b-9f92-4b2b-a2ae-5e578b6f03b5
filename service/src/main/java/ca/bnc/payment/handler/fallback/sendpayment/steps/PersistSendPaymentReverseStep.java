package ca.bnc.payment.handler.fallback.sendpayment.steps;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.handler.fallback.sendpayment.SendPaymentFallbackStep;
import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.repository.OutgoingPaymentRepository;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.PaymentStatus.REVERSED_PAYMENT_STATUS;
import static ca.bnc.payment.constant.SendFallbackStepOrderValues.SEND_FALLBACK_ORDER_THIRTY;

@Component
@Order(SEND_FALLBACK_ORDER_THIRTY)
@RequiredArgsConstructor
public class PersistSendPaymentReverseStep implements SendPaymentFallbackStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PersistSendPaymentReverseStep.class);

    private final OutgoingPaymentRepository outgoingPaymentRepository;
    private final LoggingFacade loggingFacade;

    @Override
    public boolean isApplicable(final SendPaymentContext sendPaymentContext) {
        return sendPaymentContext.isDoPersistReverseStatus();
    }

    @Override
    public boolean apply(final SendPaymentContext sendPaymentContext) {
        boolean isUpdated = false;
        try {
            final OutgoingDomesticPayment outgoingDomesticPayment = sendPaymentContext.getTaskContext().getOutgoingDomesticPayment();
            sendPaymentContext.getTaskContext().setProcessingStatus(REVERSED_PAYMENT_STATUS);
            outgoingDomesticPayment.setProcessingStatus(REVERSED_PAYMENT_STATUS);
            outgoingPaymentRepository.update(outgoingDomesticPayment);
            isUpdated = true;
        } catch (final Exception fallBackException) {
            if (fallBackException instanceof OutgoingPaymentException outgoingPaymentException) {
                sendPaymentContext.setOutgoingPaymentException(outgoingPaymentException);
            }
            loggingFacade.error(LOGGER, fallBackException);
        }
        return isUpdated;
    }
}
