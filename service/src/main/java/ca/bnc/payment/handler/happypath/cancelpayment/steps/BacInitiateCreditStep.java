package ca.bnc.payment.handler.happypath.cancelpayment.steps;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.facade.BankAccountConnectorFacade;
import ca.bnc.payment.handler.happypath.cancelpayment.CancelPaymentStep;
import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.bnc.payment.repository.OutgoingPaymentRepository;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CreditResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static ca.bnc.payment.constant.CancelStepOrderValues.CANCEL_ORDER_TEN;
import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_ACCOUNT_CREDIT_INITIATED_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_ACCOUNT_CREDIT_INITIATING_STATUS;

@Component
@RequiredArgsConstructor
@Order(CANCEL_ORDER_TEN)
public class BacInitiateCreditStep implements CancelPaymentStep {

    private final OutgoingPaymentRepository outgoingPaymentRepository;
    private final BankAccountConnectorFacade bankAccountConnectorFacade;

    @Override
    public void apply(final CancelPaymentContext cancelPaymentContext) {

        cancelPaymentContext.getTaskContext().setProcessingStatus(CANCEL_PAYMENT_ACCOUNT_CREDIT_INITIATING_STATUS);
        updateProcessingStatus(cancelPaymentContext.getTaskContext().getOutgoingDomesticPayment());
        callInitiateCredit(cancelPaymentContext);

    }

    private void callInitiateCredit(final CancelPaymentContext cancelPaymentContext) {
        try {
            CreditResponse initiateCreditResponse = bankAccountConnectorFacade.initiateCredit(cancelPaymentContext);
            cancelPaymentContext.setInitiateCreditResponse(initiateCreditResponse);
            cancelPaymentContext.getTaskContext().setProcessingStatus(CANCEL_PAYMENT_ACCOUNT_CREDIT_INITIATED_STATUS);
        } catch (OutgoingPaymentException e) {
            cancelPaymentContext.setDoSetPersistReversedStatus(true);
            throw e;
        }
    }

    private void updateProcessingStatus(final OutgoingDomesticPayment outgoingDomesticPayment) {
        outgoingDomesticPayment.setProcessingStatus(CANCEL_PAYMENT_ACCOUNT_CREDIT_INITIATING_STATUS);
        outgoingPaymentRepository.update(outgoingDomesticPayment);
    }
}
