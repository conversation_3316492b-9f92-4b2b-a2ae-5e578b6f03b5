package ca.bnc.payment.constant;

public final class PaymentManagementErrorMessage {

    public static final String EXISTING_PAYMENT_ERROR =
            "The client's request is formed correctly, but the requested send payment operation is already processing";
    public static final String INVALID_AUTO_DEPOSIT_ERROR =
            "supplementaryData.creditorAutoDepositRegNumber field is mandatory for an auto-deposit payment.";

    public static final String THREAD_CREATION_ERROR =
            "The service encountered an unexpected condition that prevented it from fulfilling the %s Payment request.";

    public static final String CANCEL_PAYMENT_NOT_FOUND = "Payment to cancel Not Found";
    public static final String CANCEL_PAYMENT_STATUS_INVALID = "Payment is not in a valid status [%s]";

    public static final String NOTIFICATION_PAYMENT_NOT_FOUND = "The e-Transfer Payment identified by : %s cannot be found.";

    public static final String PAYMENT_ALREADY_CANCELED = "Payment is already canceled";

    private PaymentManagementErrorMessage() { /* empty */ }
}
