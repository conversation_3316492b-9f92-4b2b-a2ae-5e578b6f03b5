package ca.bnc.payment.constant;

public final class PaymentManagementErrorCodes {

    public static final String REQUEST_INVALID = "REQUEST_INVALID";
    public static final String TECHNICAL_ERROR = "TECHNICAL_ERROR";
    public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
    public static final String CREDITOR_PENDING_APPROVAL = "CREDITOR_PENDING_APPROVAL";
    public static final String CREDITOR_DELETED = "CREDITOR_DELETED";
    public static final String NON_RETRYABLE_ERROR = "NON_RETRYABLE_ERROR";
    public static final String NOT_FOUND = "NOT_FOUND";
    public static final String NOT_PROVIDED = "NOTPROVIDED";
    public static final String TRANSF_NOT_EXIST = "TRANSF_NOT_EXIST";
    public static final String PAYMENT_STATUS_INVALID = "PAYMENT_STATUS_INVALID";
    public static final String PAYMENT_ALREADY_CANCELED_ERROR = "PAYMENT_ALREADY_CANCELED";
    public static final String CALLBACK_URL_NOT_AUTHORIZED = "CALLBACK_URL_NOT_AUTHORIZED";

    private PaymentManagementErrorCodes() { /* empty */ }
}
