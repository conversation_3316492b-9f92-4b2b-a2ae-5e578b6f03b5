package ca.bnc.payment.constant;

public final class CallbackMessage {

    public static final String WHITE_LIST_ERROR_MESSAGE = "The web hook call to \"%s\" is not authorized.";
    public static final String NON_RETRYABLE_ERROR_MESSAGE = "An error has occurred while storing important data."
            + " Payment identifier #%s can no longer be used to retry the payment";
    public static final String SUCCESS_MESSAGE =
            "The web hook endpoint: %s succeeded";
    public static final String CALLBACK_TIMEOUT = "The web hook endpoint: %s is unreachable";
    public static final String CALLBACK_BAD_REQUEST =
            "The web hook endpoint: %s replies with a HTTP-400 (Bad request) status code";
    public static final String CALLBACK_NOT_FOUND =
            "The web hook endpoint: %s replies with a HTTP-404 Error (Resource Not found) status code";
    public static final String CALLBACK_INTERNAL_SERVER_ERROR =
            "The web hook endpoint: %s replies with a HTTP-500 (Internal Server Error) status code";
    public static final String CALLBACK_SERVICE_UNAVAILABLE =
            "The web hook endpoint: %s replies with a HTTP-503 (Service Unavailable) status code";
    public static final String CALLBACK_GENERAL_EXCEPTION =
            "Exception calling the Send Domestic Payment Callback application, returned %s";

    private CallbackMessage() { /* empty */ }
}
