package ca.bnc.payment.constant;

public final class PartyErrorMessage {

    public static final String PARTY_API_GENERAL_EXCEPTION =
            "Exception calling Party API, returned %s";
    public static final String PARTY_API_GENERAL_EXCEPTION_NO_STATUS =
            "Exception calling Party API";
    public static final String PARTY_API_BAD_REQUEST =
            "Bad request received by the Payment Party API";
    public static final String PARTY_API_INTERNAL_SERVER_ERROR =
            "The Payment Party API encountered an unexpected condition that prevented it from fulfilling the request.";
    public static final String INTERAC_ID_NOT_FOUND =
            "The Interac User Id of the client identified by %S was not found.";
    public static final String PARTY_API_SERVICE_UNAVAILABLE =
            "The Payment Party API is not ready to handle the request.";

    private PartyErrorMessage() { /* empty */ }
}
