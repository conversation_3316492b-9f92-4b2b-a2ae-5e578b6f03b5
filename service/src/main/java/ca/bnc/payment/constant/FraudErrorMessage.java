package ca.bnc.payment.constant;

public final class FraudErrorMessage {

    public static final String FRAUD_GENERAL_EXCEPTION =
            "Exception calling ONLINE FRAUD API, returned %s";
    public static final String FRAUD_BAD_REQUEST = "Bad ONLINE FRAUD Verification request received by the ONLINE FRAUD API : %s";
    public static final String FRAUD_FORBIDDEN = "The ONLINE FRAUD API refused to execute the ONLINE FRAUD Verification request";
    public static final String FRAUD_VERIFICATION_API_SERVICE_UNAVAILABLE = "The Fraud Verification API is not ready to "
            + "handle the %s request";
    public static final String FRAUD_UNAUTHORIZED =
            "The service wants to get a protected resource from the ONLINE FRAUD API without providing the proper authorization";
    public static final String FRAUD_NOT_FOUND = "Resource not found while executing the ONLINE FRAUD Verification request";
    public static final String FRAUD_RATE_LIMITED = "The ONLINE FRAUD API blocked the ONLINE FRAUD Verification request call due to "
            + "rate limiting.";
    public static final String FRAUD_INTERNAL_SERVER_ERROR = "The ONLINE FRAUD API encountered an unexpected condition that prevented "
        + "it from fulfilling the ONLINE FRAUD Verification request";

    public static final String FRAUD_DECISION_NOT_CHECKED = "Fraud service decision is FRAUD_NOT_CHECKED";
    public static final String FRAUD_REJECTED = "Transaction is refused by the domain fraud.";

    private FraudErrorMessage() {
    }
}
