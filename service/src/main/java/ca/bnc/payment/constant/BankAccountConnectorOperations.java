package ca.bnc.payment.constant;

public final class BankAccountConnectorOperations {
    public static final String OPERATION_INITIATE_ACCOUNT_DEBIT = "INITIATE ACCOUNT DEBIT";
    public static final String OPERATION_CONFIRM_ACCOUNT_DEBIT = "CONFIRM ACCOUNT DEBIT";
    public static final String OPERATION_REVERSE_ACCOUNT_DEBIT = "REVERSE ACCOUNT DEBIT";

    public static final String OPERATION_CONFIRM_ACCOUNT_CREDIT = "CONFIRM ACCOUNT CREDIT";
    public static final String OPERATION_INITIATE_ACCOUNT_CREDIT = "INITIATE ACCOUNT CREDIT";
    public static final String OPERATION_REVERSE_ACCOUNT_CREDIT = "REVERSE ACCOUNT CREDIT";

    public static final String PATH_DEBIT = "debit";
    public static final String PATH_DEBIT_REVERSE = "debit reverse";
    public static final String PATH_CREDIT_REVERSE = "credit reverse";
    public static final String PATH_CREDIT = "credit";
    public static final String REVERSE = "reverse";

    private BankAccountConnectorOperations() { /* empty */ }
}
