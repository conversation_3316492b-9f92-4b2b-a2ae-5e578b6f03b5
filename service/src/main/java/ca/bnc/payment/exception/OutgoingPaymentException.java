package ca.bnc.payment.exception;

import ca.bnc.payment.constant.LogPattern;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static ca.bnc.payment.constant.LogPattern.LOG_MESSAGE_PATTERN_TASK_FAILED_WITH_ERRORS;

@Getter
@Setter
public class OutgoingPaymentException extends RuntimeException {

    private final List<Error> errors;

    public OutgoingPaymentException(final Exception exception, final String message) {
        super(message, exception);
        errors = Collections.emptyList();
    }

    public OutgoingPaymentException(final String message) {
        super(message);
        errors = Collections.emptyList();
    }

    public OutgoingPaymentException(final String message, final Error error) {
        super(message);
        errors = Optional.ofNullable(error)
                .map(List::of)
                .orElse(List.of());
    }

    public OutgoingPaymentException(final String message, final List<Error> errors) {
        super(message);
        this.errors = Optional.ofNullable(errors)
                .map(List::copyOf)
                .orElse(List.of());
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    @Override
    public String getMessage() {
        if (!errors.isEmpty()) {
            return String.format(
                    LOG_MESSAGE_PATTERN_TASK_FAILED_WITH_ERRORS,
                    super.getMessage(),
                    String.format(LogPattern.LOG_MESSAGE_PATTERN_WITH_ERRORS, errors));
        } else {
            return super.getMessage();
        }
    }

    public String getMessageNoError() {
        return super.getMessage();
    }


    public record Error(String code, String text) implements Serializable {

        @Override
        public String toString() {
            return String.format("Error(code=%s, text=%s)", code, text);
        }

    }

}
