package ca.bnc.payment.util;

import ca.bnc.payment.model.dto.BankAccountConnectorDto;
import ca.bnc.payment.model.task.TaskContext;
import org.springframework.stereotype.Component;

@Component
public class BankAccountConnectorDtoUtil {

    public BankAccountConnectorDto buildBankAccountConnectorDto(final TaskContext<?> taskContext) {
        return BankAccountConnectorDto.builder()
                .clientId(taskContext.getClientId())
                .requestId(taskContext.getRequestId())
                .retryIndicator(taskContext.getRetryIndicator())
                .build();
    }
}
