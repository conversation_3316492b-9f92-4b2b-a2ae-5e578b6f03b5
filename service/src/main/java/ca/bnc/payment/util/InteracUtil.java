package ca.bnc.payment.util;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class InteracUtil {

    private final ParticipantIdUtil participantIdUtil;

    private final String paymentIndirectConnectorId;

    public InteracUtil(final ParticipantIdUtil participantIdUtil,
                       @Value("${providers.interac-payments.indirectConnectorId}") final String paymentIndirectConnectorId) {
        this.participantIdUtil = participantIdUtil;
        this.paymentIndirectConnectorId = paymentIndirectConnectorId;
    }

    public String getPaymentParticipantId() {
        return participantIdUtil.getDirectParticipantIdentifier();
    }

    public String getPaymentIndirectConnectorId() {
        return paymentIndirectConnectorId;
    }

    public String getIndirectConnectorIdForHeader(final SupplementaryData.ClientTypeEnum clientType) {
        return isClientIndividualAndBncParticipant(clientType) ? null : paymentIndirectConnectorId;
    }

    public String getIndirectConnectorIdForBody(final SupplementaryData.ClientTypeEnum clientType) {
        return isClientIndividualAndBncParticipant(clientType)
                ? participantIdUtil.getDirectParticipantIdentifier()
                : paymentIndirectConnectorId;
    }

    private boolean isClientIndividualAndBncParticipant(final SupplementaryData.ClientTypeEnum clientType) {
        return clientType == SupplementaryData.ClientTypeEnum.INDIVIDUAL
                && participantIdUtil.isBncParticipant();
    }
}
