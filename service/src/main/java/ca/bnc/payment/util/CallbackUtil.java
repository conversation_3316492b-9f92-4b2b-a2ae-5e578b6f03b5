package ca.bnc.payment.util;

import ca.bnc.payment.exception.validation.InvalidCallbackWhiteListException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Set;

import static ca.bnc.payment.constant.CallbackMessage.WHITE_LIST_ERROR_MESSAGE;

@Component
public class CallbackUtil {

    private final Set<String> whitelistedUrls;

    public CallbackUtil(@Value("${providers.callback.whitelist}") final Set<String> whitelistedUrls) {
        whitelistedUrls.forEach(
                s -> {
                    if (isInvalidUrl(s)) {
                        throw new IllegalArgumentException(String.format("providers.callback.whitelist URL [%s] is invalid", s));
                    }
                });
        this.whitelistedUrls = whitelistedUrls;
    }

    public void validateWhiteList(final String xCallback) {
        if (isInvalidUrl(xCallback) || isNotWhiteListed(xCallback)) {
            throw new InvalidCallbackWhiteListException(
                    String.format(WHITE_LIST_ERROR_MESSAGE, xCallback)
            );
        }
    }

    private boolean isNotWhiteListed(final String callBackUrl) {
        return whitelistedUrls.stream()
                .noneMatch(callBackUrl::startsWith);
    }

    private boolean isInvalidUrl(final String url) {
        try {
            new URL(url).toURI();
            return false;
        } catch (URISyntaxException | MalformedURLException  e) {
            return true;
        }
    }

}
