package ca.bnc.payment.util;

import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class ChannelIdUtil {
    private static final Set<String> BNC_CHANNEL_IDS = Set.of("2777", "2778", "5156", "6502", "8131", "5327");
    private static final Set<String> BNE_CHANNEL_IDS = Set.of("8131", "5327");

    public boolean isBncChannelId(final String channelId) {
        return BNC_CHANNEL_IDS.contains(channelId);
    }

    public boolean isBneChannelId(final String channelId) {
        return BNE_CHANNEL_IDS.contains(channelId);
    }
}
