package ca.bnc.payment.adapter.async.producer;

import ca.bnc.payment.util.IdGenerator;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmt_logging_library.LogContext;
import ca.nbc.payment.pmt_logging_library.config.LoggingConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

import static ca.nbc.payment.pmt_logging_library.LogConstants.CORRELATION_ID;

@Component
public class KafkaProducer {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaProducer.class.getName());
    private static final String LOG_MESSAGING_ACTION = "PUBLISH";
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final LoggingFacade loggingFacade;
    private final LogContextHolder logContextHolder;
    private final RetryTemplate kafkaRetryTemplate;
    private final IdGenerator idGenerator;
    private final LoggingConfig loggingConfig;
    private final ObjectMapper objectMapper;

    public KafkaProducer(
            final KafkaTemplate<String, Object> kafkaProducerTemplate,
            final LogContextHolder logContextHolder,
            final LoggingFacade loggingFacade,
            final RetryTemplate kafkaRetryTemplate,
            final IdGenerator idGenerator,
            final LoggingConfig loggingConfig,
            final ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaProducerTemplate;
        this.logContextHolder = logContextHolder;
        this.loggingFacade = loggingFacade;
        this.kafkaRetryTemplate = kafkaRetryTemplate;
        this.idGenerator = idGenerator;
        this.loggingConfig = loggingConfig;
        this.objectMapper = objectMapper;
    }

    <T> void doSendMessage(final String topic, final T messageObject, final String logMessagingId) {
        final String correlationId = idGenerator.generateCorrelationId();
        Message<T> message = buildMessageWithHeader(topic, messageObject, correlationId);
        CompletableFuture<SendResult<String, Object>> future = kafkaRetryTemplate.execute(
                retryContext -> kafkaTemplate.send(message),
                retryContext -> CompletableFuture.failedFuture(retryContext.getLastThrowable())
        );
        LogContextHolder.LogContext logContext = logContextHolder.copyContext();
        future.whenComplete((result, ex) ->
                logContextHolder.runWithContext(
                        () -> handleMessageEmission(correlationId, ex, topic, message, logMessagingId),
                        logContext));
    }

    private <T> void handleMessageEmission(final String correlationId,
                                           final Throwable ex,
                                           final String topic,
                                           final Message<T> message,
                                           final String logMessagingId) {
        logContextHolder.add(CORRELATION_ID, correlationId);
        logMessage(message, logMessagingId);
        if (ex != null) {
            loggingFacade.error(LOGGER, "Error sending Kafka message to " + topic, ex);
        } else {
            loggingFacade.info(LOGGER, "Kafka message sent successfully to " + topic);
        }
    }

    private <T> Message<T> buildMessageWithHeader(final String topic, final T messageObject, final String correlationId) {
        return MessageBuilder.withPayload(messageObject)
                .setHeader(KafkaHeaders.TOPIC, topic)
                .setHeader(KafkaHeaders.CORRELATION_ID, correlationId)
                .build();
    }

    private <T> void logMessage(final Message<T> message, final String logMessagingId) {
        try {
            loggingFacade.logMessagingKafka(LOGGER,
                    LogContext.SENDING_MESSAGE.getValue(),
                    LOG_MESSAGING_ACTION,
                    logMessagingId,
                    message.getHeaders().entrySet().stream(),
                    objectMapper.writeValueAsString(message.getPayload()),
                    loggingConfig);
        } catch (Exception e) {
            loggingFacade.error(LOGGER, "Error logging Kafka message", e);
        }
    }
}
