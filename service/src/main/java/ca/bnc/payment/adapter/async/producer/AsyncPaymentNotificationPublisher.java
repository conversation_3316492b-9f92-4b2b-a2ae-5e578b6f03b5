package ca.bnc.payment.adapter.async.producer;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.ETransferPaymentProcessingEvent;
import ca.nbc.payment.lib.service.logging.LogContextHolder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class AsyncPaymentNotificationPublisher {

    private final String paymentNotificationTopic;
    private final KafkaProducer kafkaProducer;
    private final LogContextHolder logContextHolder;
    private static final String LOG_MESSAGING_ID = "PAYMENT_NOTIFICATION";

    public AsyncPaymentNotificationPublisher(@Value("${kafka.topics.payment-notification}") final String paymentNotificationTopic,
                                             final KafkaProducer kafkaProducer,
                                             final LogContextHolder logContextHolder) {
        this.paymentNotificationTopic = paymentNotificationTopic;
        this.kafkaProducer = kafkaProducer;
        this.logContextHolder = logContextHolder;
    }

    @Async("workerThreadExecutor")
    public void sendMessage(final ETransferPaymentProcessingEvent messageObject,
                            final LogContextHolder.LogContext logContext) {
        logContextHolder.runWithContext(
                () -> kafkaProducer.doSendMessage(paymentNotificationTopic, messageObject, LOG_MESSAGING_ID),
                logContext);
    }
}
