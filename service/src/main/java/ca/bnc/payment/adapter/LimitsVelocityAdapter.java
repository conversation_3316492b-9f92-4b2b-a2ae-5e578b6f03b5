package ca.bnc.payment.adapter;

import ca.bnc.payment.client.limitsvelocity.LimitsVelocityApiClient;
import ca.bnc.payment.exception.limitsvelocity.RetryableLimitsVelocityException;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesDecrement;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesIncrement;
import lombok.RequiredArgsConstructor;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LimitsVelocityAdapter {

    private final LimitsVelocityApiClient limitsVelocityApiClient;

    @Retryable(retryFor = {RetryableLimitsVelocityException.class},
            maxAttemptsExpression = "${providers.limits-velocity.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.limits-velocity.retry.backoff}"))
    public void incrementVelocities(final String xChannelId,
                                    final String acceptVersion,
                                    final String xRequestId,
                                    final VelocitiesIncrement velocitiesIncrementRequest) {
        limitsVelocityApiClient.incrementVelocities(xChannelId, acceptVersion, xRequestId, velocitiesIncrementRequest);
    }

    @Retryable(retryFor = {RetryableLimitsVelocityException.class},
            maxAttemptsExpression = "${providers.limits-velocity.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.limits-velocity.retry.backoff}"))
    public void decrementVelocities(final String xChannelId,
                                    final String acceptVersion,
                                    final String xRequestId,
                                    final VelocitiesDecrement velocitiesDecrementRequest) {
        limitsVelocityApiClient.decrementVelocities(xChannelId, acceptVersion, xRequestId, velocitiesDecrementRequest);
    }

}
