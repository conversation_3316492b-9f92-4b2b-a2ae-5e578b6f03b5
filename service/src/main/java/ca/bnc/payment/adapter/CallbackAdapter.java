package ca.bnc.payment.adapter;

import ca.bnc.payment.client.callback.SendPaymentCallbackClient;
import ca.bnc.payment.exception.callback.RetryableCallbackException;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.CallbackSendRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CallbackAdapter {

    @Retryable(retryFor = RetryableCallbackException.class,
            maxAttemptsExpression = "${providers.callback.retry.attempts}",
            backoff = @Backoff(delayExpression = "${providers.callback.retry.backoff}"))
    public void sendPaymentCallback(final SendPaymentCallbackClient callbackClient,
                                    final String endToEndIdentification,
                                    final String acceptVersion,
                                    final String xRequestId,
                                    final CallbackSendRequest request) {

        callbackClient.callbackTransfer(
                endToEndIdentification,
                acceptVersion,
                xRequestId,
                request);
    }
}
