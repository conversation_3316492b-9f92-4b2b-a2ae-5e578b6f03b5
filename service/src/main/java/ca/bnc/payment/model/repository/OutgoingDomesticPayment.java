package ca.bnc.payment.model.repository;

import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import java.math.BigDecimal;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamoDbBean
@SuppressWarnings({"findbugs:EI_EXPOSE_REP", "findbugs:EI_EXPOSE_REP2"})
public class OutgoingDomesticPayment {

    @Getter(onMethod_ = {@DynamoDbPartitionKey, @DynamoDbAttribute("end_to_end_identification")})
    @Setter(onMethod_ = {@JsonSetter("end_to_end_identification")})
    private String endToEndIdentification;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_created_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_created_date")})
    private String sysCreatedDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_modified_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_modified_date")})
    private String sysModifiedDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("sys_expiration_date")})
    @Setter(onMethod_ = {@JsonSetter("sys_expiration_date")})
    private Long sysExpirationDate;

    @Getter(onMethod_ = {@DynamoDbAttribute("processing_status")})
    @Setter(onMethod_ = {@JsonSetter("processing_status")})
    private String processingStatus;

    @Getter(onMethod_ = {@DynamoDbAttribute("channel_id")})
    @Setter(onMethod_ = {@JsonSetter("channel_id")})
    private String channelId;

    @Getter(onMethod_ = {@DynamoDbAttribute("etransfer_id")})
    @Setter(onMethod_ = {@JsonSetter("etransfer_id")})
    private String eTransferId;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_interac_id")})
    @Setter(onMethod_ = {@JsonSetter("debtor_interac_id")})
    private String debtorInteracId;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_account_no")})
    @Setter(onMethod_ = {@JsonSetter("debtor_account_no")})
    private String debtorAccountNo;

    @Getter(onMethod_ = {@DynamoDbAttribute("debtor_id")})
    @Setter(onMethod_ = {@JsonSetter("debtor_id")})
    private String debtorId;

    @Getter(onMethod_ = {@DynamoDbAttribute("payment_type")})
    @Setter(onMethod_ = {@JsonSetter("payment_type")})
    private String paymentType;

    @Getter(onMethod_ = {@DynamoDbAttribute("amount")})
    @Setter(onMethod_ = {@JsonSetter("amount")})
    private BigDecimal amount;

    @Getter(onMethod_ = {@DynamoDbAttribute("currency")})
    @Setter(onMethod_ = {@JsonSetter("currency")})
    private String currency;

}
