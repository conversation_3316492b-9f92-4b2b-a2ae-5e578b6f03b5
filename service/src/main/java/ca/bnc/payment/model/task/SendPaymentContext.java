package ca.bnc.payment.model.task;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.OriginalGroupInformation29;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class SendPaymentContext {

    private TaskContext<SendPaymentRequest> taskContext;
    private OriginalGroupInformation29 initiateBankAccountDebitResponseOriginalGroupInfo;
    private String initiateInteracPaymentResponseETransferId;
    private String initiateInteracPaymentResponsePaymentTransactionToken;
    private String initiateInteracPaymentResponseExtFraudAction;
    private OutgoingPaymentException outgoingPaymentException;

    // Status
    private boolean sendPaymentCompleted;

    // Flag for rollback steps
    private boolean doStopInteracPayment;
    private boolean doReverseBankAccountDebit;
    private boolean doPersistReverseStatus;
    private boolean doDecrementLimitVelocities;
}
