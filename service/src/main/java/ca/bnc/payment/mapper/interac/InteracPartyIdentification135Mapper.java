package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.Contact4;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.GenericOrganisationIdentification1;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.OrganisationIdentification29;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.Party38Choice;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PartyIdentification135;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorContact;
import ca.nbc.payment.pmtpartnersparty.model.Identifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

@Component
public class InteracPartyIdentification135Mapper {

    public PartyIdentification135 map(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorIdentification
                    creditorIdentification) {

        return new PartyIdentification135()
                .name(creditorIdentification.getName())
                .contactDetails(buildContactDetails(
                        creditorIdentification.getName(),
                        creditorIdentification.getContactDetails()));

    }

    public PartyIdentification135 map(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorIdentification
                    debtorIdentification,
            final Identifier activeIdentifier) {

        return new PartyIdentification135()
                .name(debtorIdentification.getName())
                .identification(
                        buildIdentification(activeIdentifier))
                .contactDetails(
                        buildContactDetails(
                                debtorIdentification.getName(),
                                debtorIdentification.getContactDetails()));
    }

    private Party38Choice buildIdentification(final Identifier activeIdentifier) {

        return Optional.ofNullable(activeIdentifier)
                .map(identification1 ->
                        new Party38Choice()
                                .organisationIdentification(new OrganisationIdentification29()
                                        .other(Collections.singletonList(new GenericOrganisationIdentification1()
                                                .identification(identification1.getValue())))))
                .orElse(null);

    }

    private Contact4 buildContactDetails(final String name, final CreditorContact creditorContact) {

        if (creditorContact == null) {
            return new Contact4().name(name);
        }

        return new Contact4()
                .name(name)
                .mobileNumber(creditorContact.getMobileNumber())
                .emailAddress(creditorContact.getEmailAddress());

    }

    private Contact4 buildContactDetails(final String name, final DebtorContact debtorContact) {

        if (debtorContact == null) {
            return new Contact4().name(name);
        }

        return new Contact4()
                .name(name)
                .mobileNumber(debtorContact.getMobileNumber())
                .emailAddress(debtorContact.getEmailAddress());
    }

}
