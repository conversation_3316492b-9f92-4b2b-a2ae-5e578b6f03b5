package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.AccountIdentification4Choice;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdAccountIdentificationMapper {

    private final PptdGenericAccountIdentificationMapper pptdGenericAccountIdentificationMapper;

    public AccountIdentification4Choice mapAccountIdentification(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.AccountIdentification4Choice
                    accountIdentification
    ) {
        return new AccountIdentification4Choice()
                .other(pptdGenericAccountIdentificationMapper.mapGenericAccountIdentification(accountIdentification.getOther()));
    }

}
