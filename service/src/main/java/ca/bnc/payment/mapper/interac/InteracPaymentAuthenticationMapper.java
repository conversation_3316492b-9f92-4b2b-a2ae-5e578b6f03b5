package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.AuthenticationType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.HashType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentAuthentication;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.LocalInstrument2Choice;
import org.springframework.stereotype.Component;

@Component
public class InteracPaymentAuthenticationMapper {

    public PaymentAuthentication map(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentAuthentication
                    paymentAuthentication, final LocalInstrument2Choice.ProprietaryEnum proprietary) {
        if (proprietary == LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT) {
            return new PaymentAuthentication()
                    .authenticationType(AuthenticationType.PAYMENT_LEVEL)
                    .securityQuestion(paymentAuthentication.getSecurityQuestion())
                    .hashType(HashType.fromValue(paymentAuthentication.getHashType().getValue()))
                    .hashSalt(paymentAuthentication.getHashSalt())
                    .securityAnswer(paymentAuthentication.getSecurityAnswer());
        } else {
            return new PaymentAuthentication()
                    .authenticationType(AuthenticationType.NOT_REQUIRED);
        }
    }
}
