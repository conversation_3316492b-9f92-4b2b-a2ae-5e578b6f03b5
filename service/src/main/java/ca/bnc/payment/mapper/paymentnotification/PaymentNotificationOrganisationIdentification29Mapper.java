package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.GenericOrganisationIdentification1;
import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.OrganisationIdentification29;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Component
@RequiredArgsConstructor
public class PaymentNotificationOrganisationIdentification29Mapper {

    private final PaymentNotificationGenericOrganisationIdentification1Mapper paymentNotificationGenericOrganisationIdentification1Mapper;

    public OrganisationIdentification29 mapOrganisationIdentification(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.OrganisationIdentification29
                    organisationIdentification
    ) {
        return new OrganisationIdentification29()
                .other(mapGenericOrganisationIdentificationList(organisationIdentification.getOther()));
    }

    private List<GenericOrganisationIdentification1> mapGenericOrganisationIdentificationList(
            final Collection<ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GenericOrganisationIdentification1>
                    sourceList) {
        return sourceList.stream()
                .map(paymentNotificationGenericOrganisationIdentification1Mapper::mapGenericOrganisationIdentification)
                .toList();
    }

}
