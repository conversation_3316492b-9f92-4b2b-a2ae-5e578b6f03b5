package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader91;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class BacGroupHeader91Mapper {

    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";

    private final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;
    private final String directParticipantIdentifier;

    public BacGroupHeader91Mapper(
            final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper,
            @Value("${application.directParticipantIdentifier}") final String directParticipantIdentifier) {
        this.bacBranchAndFinancialInstitutionIdentification6Mapper = bacBranchAndFinancialInstitutionIdentification6Mapper;
        this.directParticipantIdentifier = directParticipantIdentifier;
    }

    public GroupHeader91 map(final DomesticCancellationPaymentRequest domesticCancellationPaymentRequest) {
        return new GroupHeader91()
                .messageIdentification(domesticCancellationPaymentRequest.getGroupHeader().getMessageIdentification())
                .creationDatetime(domesticCancellationPaymentRequest.getGroupHeader().getCreationDateTime())
                .instructingAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(directParticipantIdentifier))
                .instructedAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION));
    }

    public GroupHeader91 map(final FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer) {

        return new GroupHeader91()
                .messageIdentification(fiToFICustomerCreditTransfer.getGroupHeader().getMessageIdentification())
                .creationDatetime(fiToFICustomerCreditTransfer.getGroupHeader().getCreationDateTime())
                .instructingAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(directParticipantIdentifier))
                .instructedAgent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION));
    }

}
