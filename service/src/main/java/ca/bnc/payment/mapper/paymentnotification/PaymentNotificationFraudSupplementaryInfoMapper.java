package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.SupplementaryInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentNotificationFraudSupplementaryInfoMapper {

    public SupplementaryInfo mapFraudSupplementaryInfo(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryInfo fraudSupplementaryInfo) {
        return new SupplementaryInfo()
                .clientIpAddress(fraudSupplementaryInfo.getClientIpAddress())
                .clientCardNumber(fraudSupplementaryInfo.getClientCardNumber())
                .clientDeviceFingerPrint(fraudSupplementaryInfo.getClientDeviceFingerPrint())
                .clientAuthenticationMethod(
                        SupplementaryInfo.ClientAuthenticationMethodEnum.fromValue(
                                fraudSupplementaryInfo.getClientAuthenticationMethod().getValue()))
                .accountCreationDate(fraudSupplementaryInfo.getAccountCreationDate());
    }
}
