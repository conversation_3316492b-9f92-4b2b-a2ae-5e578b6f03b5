package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.GroupHeader93;
import ca.bnc.payment.util.InteracUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InteracGroupHeader93Mapper {

    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";

    private final InteracSettlementInstruction7Mapper interacSettlementInstruction7Mapper;
    private final InteracBranchAndFinancialInstitutionIdentification6Mapper interacBranchAndFinancialInstitutionIdentification6Mapper;
    private final InteracUtil interacUtil;

    public GroupHeader93 map(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GroupHeader93 groupHeader) {

        return new GroupHeader93()
                .messageIdentification(groupHeader.getMessageIdentification())
                .creationDatetime(groupHeader.getCreationDateTime())
                .numberOfTransactions(groupHeader.getNumberOfTransactions())
                .settlementInformation(interacSettlementInstruction7Mapper.map())
                .instructingAgent(interacBranchAndFinancialInstitutionIdentification6Mapper.map(interacUtil.getPaymentParticipantId()))
                .instructedAgent(interacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION));
    }

}
