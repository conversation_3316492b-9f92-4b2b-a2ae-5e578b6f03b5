package ca.bnc.payment.mapper.callback;

import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.CallbackSendRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.Errors;
import org.springframework.stereotype.Component;

@Component
public class CallbackSendRequestMapper {

    public CallbackSendRequest map(final Errors errors) {
        final CallbackSendRequest callbackSendRequest = new CallbackSendRequest(CallbackSendRequest.StatusEnum.SUCCESS);

        if (errors != null) {
            callbackSendRequest.status(CallbackSendRequest.StatusEnum.ERROR);
            callbackSendRequest.errors(errors.getErrors());
        }

        return callbackSendRequest;
    }
}
