package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.GenericAccountIdentification1;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentNotificationGenericAccountIdentificationMapper {

    public GenericAccountIdentification1 mapGenericAccountIdentification(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GenericAccountIdentification1
                    genericAccountIdentification
    ) {
        return new GenericAccountIdentification1()
                .identification(genericAccountIdentification.getIdentification());
    }

}
