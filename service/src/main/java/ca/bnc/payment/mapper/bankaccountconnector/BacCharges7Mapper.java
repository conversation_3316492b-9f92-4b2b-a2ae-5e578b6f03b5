package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Charges7;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class BacCharges7Mapper {

    private final BacActiveOrHistoricCurrencyAndAmountMapper bacActiveOrHistoricCurrencyAndAmountMapper;
    private final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;
    private final String directParticipantIdentifier;

    public BacCharges7Mapper(
            final BacActiveOrHistoricCurrencyAndAmountMapper bacActiveOrHistoricCurrencyAndAmountMapper,
            final BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper,
            @Value("${application.directParticipantIdentifier}") final String directParticipantIdentifier) {
        this.bacActiveOrHistoricCurrencyAndAmountMapper = bacActiveOrHistoricCurrencyAndAmountMapper;
        this.bacBranchAndFinancialInstitutionIdentification6Mapper = bacBranchAndFinancialInstitutionIdentification6Mapper;
        this.directParticipantIdentifier = directParticipantIdentifier;
    }

    public Charges7 map(final OutgoingDomesticPayment outgoingDomesticPayment) {
        return new Charges7()
                .amount(bacActiveOrHistoricCurrencyAndAmountMapper.map(outgoingDomesticPayment))
                .agent(bacBranchAndFinancialInstitutionIdentification6Mapper.map(directParticipantIdentifier));
    }

}
