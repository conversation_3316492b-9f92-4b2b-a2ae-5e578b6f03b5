package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorIdentification;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorIdentification;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GenericOrganisationIdentification1;
import ca.nbc.payment.etransfer.bankaccountconnector.model.OrganisationIdentification29;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Party38ChoiceOrganizationIdentification;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PartyIdentification135;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static ca.bnc.payment.constant.BankAccountConnectorObjectTypes.ORGANISATION_IDENTIFICATION;
import static ca.bnc.payment.constant.PaymentManagementErrorCodes.NOT_PROVIDED;

@RequiredArgsConstructor
@Component
public class BacPartyIdentification135Mapper {

    private final BacParty38ChoiceMapper bacParty38ChoiceMapper;
    private final BacContact4Mapper bacContact4Mapper;

    public PartyIdentification135 mapDefaultPartyIdentification() {
        List<GenericOrganisationIdentification1> others = new ArrayList<>();
        others.add(new GenericOrganisationIdentification1().identification(NOT_PROVIDED));

        return new PartyIdentification135()
                .identification(new Party38ChoiceOrganizationIdentification()
                        .objectType(ORGANISATION_IDENTIFICATION)
                        .organisationIdentification(new OrganisationIdentification29().other(others)));
    }

    public PartyIdentification135 map(final DebtorIdentification debtor) {

        return new PartyIdentification135()
                .name(debtor.getName())
                .identification(bacParty38ChoiceMapper.map(debtor.getIdentification()))
                .contactDetails(bacContact4Mapper.map(debtor.getName(), debtor.getContactDetails()));
    }

    public PartyIdentification135 map(final CreditorIdentification creditor) {

        return Optional.ofNullable(creditor)
                .map(creditor1 ->
                        new PartyIdentification135()
                                .name(creditor1.getName())
                                .contactDetails(bacContact4Mapper.map(creditor1.getName(), creditor1.getContactDetails())))
                .orElse(null);

    }

}
