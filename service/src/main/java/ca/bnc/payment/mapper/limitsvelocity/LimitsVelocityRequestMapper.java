package ca.bnc.payment.mapper.limitsvelocity;

import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.ClientType;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesDecrement;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesIncrement;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import ca.bnc.payment.util.TimeService;
import ca.bnc.payment.util.IdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import static ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_ANR;
import static ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_MONEYREQUEST;
import static ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_REGULAR;
import static ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType.DOMESTIC_INTERAC_RTANR;

@Component
@RequiredArgsConstructor
public class LimitsVelocityRequestMapper {

    private final TimeService timeService;
    private final IdGenerator idGenerator;

    public VelocitiesIncrement mapIncrement(final SendPaymentContext sendPaymentContext) {
        CreditTransferTransaction39 creditTransferTransactionInformation = sendPaymentContext
                .getTaskContext()
                .getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation();
        return new VelocitiesIncrement()
                .amount(getAmount(creditTransferTransactionInformation))
                .clientId(sendPaymentContext.getTaskContext().getClientId())
                .clientType(getClientType(creditTransferTransactionInformation))
                .limitType(getLimitType(creditTransferTransactionInformation))
                .endToEndIdentification(getEndToEndIdentification(creditTransferTransactionInformation))
                .instructionIdentification(getInstructionIdentification(creditTransferTransactionInformation))
                .postingDate(getPostingDate());
    }

    public VelocitiesDecrement mapDecrement(final SendPaymentContext sendPaymentContext) {
        CreditTransferTransaction39 creditTransferTransactionInformation = sendPaymentContext
                .getTaskContext()
                .getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation();
        return new VelocitiesDecrement()
                .endToEndIdentification(getEndToEndIdentification(creditTransferTransactionInformation))
                .instructionIdentification(generateNewCommandId())
                .originalInstructionIdentification(getInstructionIdentification(creditTransferTransactionInformation));
    }

    private ClientType getClientType(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        SupplementaryData.ClientTypeEnum clientTypeEnum = creditTransferTransactionInformation
                .getSupplementaryData()
                .getClientType();
        return SupplementaryData.ClientTypeEnum.INDIVIDUAL == clientTypeEnum ? ClientType.INDIVIDUAL : ClientType.ORGANISATION;
    }

    private BigDecimal getAmount(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        return creditTransferTransactionInformation
                .getInterbankSettlementAmount()
                .getAmount();
    }

    private LimitType getLimitType(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        LocalInstrument2Choice.ProprietaryEnum limitTypeEnum = creditTransferTransactionInformation
                .getPaymentTypeInformation()
                .getLocalInstrument()
                .getProprietary();

        return switch (limitTypeEnum) {
            case ACCOUNT_DEPOSIT_PAYMENT -> DOMESTIC_INTERAC_ANR;
            case REALTIME_ACCOUNT_DEPOSIT_PAYMENT -> DOMESTIC_INTERAC_RTANR;
            case REGULAR_PAYMENT, ACCOUNT_ALIAS_PAYMENT, REALTIME_ACCOUNT_ALIAS_PAYMENT -> DOMESTIC_INTERAC_REGULAR;
            case FULFILL_REQUEST_FOR_PAYMENT -> DOMESTIC_INTERAC_MONEYREQUEST;
        };
    }

    private String getEndToEndIdentification(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        return creditTransferTransactionInformation
                .getPaymentIdentification()
                .getEndToEndIdentification();
    }

    private String getInstructionIdentification(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        return creditTransferTransactionInformation
                .getPaymentIdentification()
                .getInstructionIdentification();
    }

    private OffsetDateTime getPostingDate() {
        return timeService.getNowOffsetDateTime();
    }

    private String generateNewCommandId() {
        return idGenerator.generateCommandId();
    }
}
