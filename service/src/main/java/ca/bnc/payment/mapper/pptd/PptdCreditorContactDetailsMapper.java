package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.Contact4;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorContact;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdCreditorContactDetailsMapper {

    public Contact4 mapContactDetails(final CreditorContact creditorContact) {
        return new Contact4()
                .mobileNumber(creditorContact.getMobileNumber())
                .emailAddress(creditorContact.getEmailAddress());
    }

}
