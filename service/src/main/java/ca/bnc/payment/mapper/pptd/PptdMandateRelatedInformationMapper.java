package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.MandateRelatedInformation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdMandateRelatedInformationMapper {

    public MandateRelatedInformation mapMandateRelatedInformation(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.MandateRelatedInformation
                    mandateRelatedInformation) {
        return new MandateRelatedInformation()
                .mandateIdentification(mandateRelatedInformation.getMandateIdentification())
                .frequencyTypePeriod(
                        MandateRelatedInformation.FrequencyTypePeriodEnum.fromValue(
                                mandateRelatedInformation.getFrequencyTypePeriod().getValue()))
                .countPerPeriod(mandateRelatedInformation.getCountPerPeriod())
                .numberOfRemaining(mandateRelatedInformation.getSupplementaryData().getNumberOfRemaining())
                .currentOccurrence(mandateRelatedInformation.getSupplementaryData().getCurrentOccurrence());
    }
}
