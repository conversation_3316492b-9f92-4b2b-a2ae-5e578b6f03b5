package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemMemberIdentification2;
import ca.nbc.payment.etransfer.bankaccountconnector.model.FinancialInstitutionIdentification18;
import org.springframework.stereotype.Component;

@Component
public class BacBranchAndFinancialInstitutionIdentification6Mapper {

    public BranchAndFinancialInstitutionIdentification6 map(final String systemMemberIdentification) {
        return new BranchAndFinancialInstitutionIdentification6()
                .financialInstitutionIdentification(financialInstitutionIdentification(systemMemberIdentification));
    }

    private FinancialInstitutionIdentification18 financialInstitutionIdentification(final String systemMemberIdentification) {
        return new FinancialInstitutionIdentification18()
                .clearingSystemMemberIdentification(clearingSystemMemberIdentification(systemMemberIdentification));
    }

    private ClearingSystemMemberIdentification2 clearingSystemMemberIdentification(final String systemMemberIdentification) {
        return new ClearingSystemMemberIdentification2()
                .memberIdentification(systemMemberIdentification);
    }
}
