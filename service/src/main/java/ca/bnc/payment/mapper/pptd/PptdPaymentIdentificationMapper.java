package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.PaymentIdentification7;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdPaymentIdentificationMapper {

    public PaymentIdentification7 mapPaymentIdentification(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7 paymentIdentification,
            final String confirmationId
    ) {
        return new PaymentIdentification7()
                .instructionIdentification(paymentIdentification.getInstructionIdentification())
                .endToEndIdentification(confirmationId);
    }

}
