package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.ChannelType;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.ClientType;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.DomesticETransferEvent;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.DomesticETransferEventRawData;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.ExternalPaymentTransactionStatus1Code;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.PaymentTypeCode;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.SupplementaryData;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.Error;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.util.TimeService;
import ca.bnc.payment.util.IdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.util.Optional;

import static ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.Entity.DOMESTIC_ETRANSFER_PAYMENT;
import static ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.EventType.PAYMENT_TRANSACTION_DATA_EXPORTED;


@Component
@RequiredArgsConstructor
public class PptdMapper {

    private static final String MESSAGE_DEFINITION_IDENTIFIER = "pacs.008.001.08";
    private static final String VERSION = "1.0.0";
    private final PptdMessageDataMapper pptdMessageDataMapper;
    private final PptdSupplementaryDataMapper pptdSupplementaryDataMapper;
    private final TimeService timeService;
    private final IdGenerator idGenerator;

    public DomesticETransferEvent mapPptdSendRequest(
            final RequestDto<SendPaymentRequest> requestDto,
            final String clearingSystemReference,
            final String debtorInteracId,
            final String extFraudAction,
            final Error error) {

        FIToFICustomerCreditTransferV08 customerCreditTransfer = requestDto.getRequestBody().getFiToFICustomerCreditTransfer();

        String instructionIdentification = idGenerator.generateInstructionIdentification();
        String endToEndBusinessIdentification = customerCreditTransfer
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification();
        String confirmationId = customerCreditTransfer
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getConfirmationId();

        OffsetDateTime creationDateTime = customerCreditTransfer.getGroupHeader().getCreationDateTime();
        return createCommonDomesticETransferEvent(requestDto)
                .instructionIdentification(instructionIdentification)
                .endToEndBusinessIdentification(endToEndBusinessIdentification)
                .clientType(getClientType(customerCreditTransfer.getCreditTransferTransactionInformation()))
                .paymentTypeCode(getPaymentTypeCode(customerCreditTransfer.getCreditTransferTransactionInformation()))
                .rawData(
                        createCommonRawData(instructionIdentification, endToEndBusinessIdentification, creationDateTime)
                                .clientId(requestDto.getXClientId())
                                .messageData(pptdMessageDataMapper.mapMessageData(customerCreditTransfer, confirmationId))
                                .supplementaryData(pptdSupplementaryDataMapper.mapSupplementaryData(
                                        customerCreditTransfer.getCreditTransferTransactionInformation(),
                                        clearingSystemReference,
                                        extFraudAction,
                                        debtorInteracId,
                                        Optional.ofNullable(error)
                                                .map(Error::getCode)
                                                .orElse(null),
                                        Optional.ofNullable(error)
                                                .map(Error::getText)
                                                .orElse(null)
                                ))
                );

    }

    public DomesticETransferEvent mapPptdCancelRequest(final TaskContext<DomesticCancellationPaymentRequest> cancelPaymentTaskContext) {
        final RequestDto<DomesticCancellationPaymentRequest> requestDto = cancelPaymentTaskContext.getRequestDto();

        String instructionIdentification = idGenerator.generateInstructionIdentification();
        String endToEndBusinessIdentification = cancelPaymentTaskContext.getEndToEndIdentification();

        OffsetDateTime creationDateTime = requestDto.getRequestBody().getGroupHeader().getCreationDateTime();
        return createCommonDomesticETransferEvent(requestDto)
                .instructionIdentification(instructionIdentification)
                .endToEndBusinessIdentification(endToEndBusinessIdentification)
                .paymentTypeCode(PaymentTypeCode.CANC)
                .rawData(createCommonRawData(instructionIdentification, endToEndBusinessIdentification, creationDateTime)
                                 .supplementaryData(
                                         new SupplementaryData()
                                                 .paymentDirection(SupplementaryData.PaymentDirectionEnum.IN)
                                                 .transactionStatus(ExternalPaymentTransactionStatus1Code.CANC)
                                 )
                );

    }


    private <T> DomesticETransferEvent createCommonDomesticETransferEvent(final RequestDto<T> requestDto) {
        return new DomesticETransferEvent()
                .version(VERSION)
                .entity(DOMESTIC_ETRANSFER_PAYMENT)
                .eventType(PAYMENT_TRANSACTION_DATA_EXPORTED)
                .eventTime(timeService.getNowOffsetDateTime())
                .channelId(requestDto.getXChannelId())
                .channelType(ChannelType.fromValue(requestDto.getXChannelType().getValue()))
                .clientId(requestDto.getXClientId())
                .msgDefIdr(MESSAGE_DEFINITION_IDENTIFIER);
    }

    private DomesticETransferEventRawData createCommonRawData(
            final String instructionIdentification,
            final String endToEndBusinessIdentification,
            final OffsetDateTime creationDatetime) {
        return new DomesticETransferEventRawData()
                .instructionIdentification(instructionIdentification)
                .endToEndBusinessIdentification(endToEndBusinessIdentification)
                .approvalRequired(Boolean.FALSE)
                .messageDefinitionIdentifier(MESSAGE_DEFINITION_IDENTIFIER)
                .creationDatetime(creationDatetime);
    }

    private PaymentTypeCode getPaymentTypeCode(final CreditTransferTransaction39 creditTransferTransactionInformation) {

        LocalInstrument2Choice.ProprietaryEnum proprietaryEnum =
                creditTransferTransactionInformation
                        .getPaymentTypeInformation()
                        .getLocalInstrument()
                        .getProprietary();

        return switch (proprietaryEnum) {
            case REGULAR_PAYMENT -> PaymentTypeCode.QA;
            case ACCOUNT_ALIAS_PAYMENT -> PaymentTypeCode.AD;
            case ACCOUNT_DEPOSIT_PAYMENT -> PaymentTypeCode.ANR;
            case FULFILL_REQUEST_FOR_PAYMENT -> PaymentTypeCode.FUL;
            case REALTIME_ACCOUNT_ALIAS_PAYMENT -> PaymentTypeCode.RAD;
            default -> PaymentTypeCode.INS;
        };

    }

    private ClientType getClientType(final CreditTransferTransaction39 creditTransferTransactionInformation) {
        return ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData.ClientTypeEnum.INDIVIDUAL
               == creditTransferTransactionInformation.getSupplementaryData().getClientType()
                ? ClientType.I
                : ClientType.O;
    }

}
