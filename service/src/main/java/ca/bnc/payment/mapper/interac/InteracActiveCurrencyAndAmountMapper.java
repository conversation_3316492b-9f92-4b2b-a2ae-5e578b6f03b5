package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ActiveCurrencyAndAmount;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ActiveCurrencyCode;
import org.springframework.stereotype.Component;

@Component
public class InteracActiveCurrencyAndAmountMapper {

    public ActiveCurrencyAndAmount map(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.ActiveCurrencyAndAmount
                    activeCurrencyAndAmount) {

        return new ActiveCurrencyAndAmount()
                .amount(activeCurrencyAndAmount.getAmount())
                .currency(ActiveCurrencyCode.fromValue(activeCurrencyAndAmount.getCurrency().getValue()));
    }
}
