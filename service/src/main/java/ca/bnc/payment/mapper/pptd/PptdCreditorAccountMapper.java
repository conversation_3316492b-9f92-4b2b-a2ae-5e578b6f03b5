package ca.bnc.payment.mapper.pptd;

import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.CashAccount38;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PptdCreditorAccountMapper {

    private final PptdAccountIdentificationMapper pptdAccountIdentificationMapper;

    public CashAccount38 mapCreditorAccount(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CashAccount38 creditorAccount
    ) {
        return new CashAccount38()
                .identification(pptdAccountIdentificationMapper.mapAccountIdentification(creditorAccount.getIdentification()));
    }

}
