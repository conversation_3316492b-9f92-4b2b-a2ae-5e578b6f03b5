package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.SupplementaryData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class PaymentNotificationSupplementaryDataMapper {

    private final PaymentNotificationFraudSupplementaryInfoMapper paymentNotificationFraudSupplementaryInfoMapper;
    private final PaymentNotificationPaymentAuthenticationMapper paymentNotificationAuthenticationMapper;

    public SupplementaryData mapSupplementaryData(
            final ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData supplementaryData
    ) {

        return new SupplementaryData()
                .creditorPreferredLanguage(Optional.ofNullable(supplementaryData.getCreditorPreferredLanguage())
                        .map(
                                ca.bnc.payment.pmt_outgoing_payment_management_resources
                                        .generated
                                        .model
                                        .SupplementaryData
                                        .CreditorPreferredLanguageEnum::getValue)
                        .map(SupplementaryData.CreditorPreferredLanguageEnum::valueOf)
                        .orElse(null))
                .interacMoneyRequestId(supplementaryData.getInteracMoneyRequestId())
                .accountHolderName(supplementaryData.getAccountHolderName())
                .fraudSupplementaryInfo(
                        paymentNotificationFraudSupplementaryInfoMapper
                                .mapFraudSupplementaryInfo(supplementaryData.getFraudSupplementaryInfo()))
                .paymentAuthentication(
                        Optional.ofNullable(supplementaryData.getPaymentAuthentication())
                                .map(paymentNotificationAuthenticationMapper::mapPaymentAuthentication)
                                .orElse(null))
                .clientType(SupplementaryData.ClientTypeEnum.valueOf(supplementaryData.getClientType().getValue()))
                .creditorId(supplementaryData.getCreditorId());
    }

}
