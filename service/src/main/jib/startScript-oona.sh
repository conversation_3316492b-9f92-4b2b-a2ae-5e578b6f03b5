
#!/bin/bash
# Do not activate by default, since it's a security risk
set -x

echo "Running init script"

echo "Step 1 Create Keystore for Interac"

# Environment variables
export KEYSTORE_PASSWORD=`openssl rand -base64 20`

# Setting up ssl directory
rm -f /etc/ssl/internal/etapi-sign-key-ca.jks
rm -f /etc/ssl/internal/server.p12

mkdir -pv /etc/ssl/internal

echo "========================================================"
echo "Environment variable KAFKA_SECRET_MSK"
echo ${KAFKA_SECRET_MSK}
echo "Environment variable KAFKA_SECRET_MSK"
echo "========================================================"

echo "========================================================"
echo "Environment variable INTERAC_JWT_OUTBOUND_SIGNING_KEY"
echo ${INTERAC_JWT_OUTBOUND_SIGNING_KEY}
echo "========================================================"

echo "========================================================"
echo "Environment variable INTERAC_JWT_OUTBOUND_SIGNING_CERT"
echo ${INTERAC_JWT_OUTBOUND_SIGNING_CERT}
echo "========================================================"

# JWS for Interac
echo "${INTERAC_JWT_OUTBOUND_SIGNING_KEY}" | base64 -d  > "/etc/ssl/internal/interac_outbound_private_key.pem"
echo "${INTERAC_JWT_OUTBOUND_SIGNING_CERT}" | base64 -d  > "/etc/ssl/internal/interac_outbound_certificate.pem"

# Store private key and cert in pkcs12 format
openssl pkcs12 -export -in "/etc/ssl/internal/interac_outbound_certificate.pem" -inkey "/etc/ssl/internal/interac_outbound_private_key.pem" -out "/etc/ssl/internal/server.p12" -password pass:${KEYSTORE_PASSWORD}

# create keystore jks
keytool -importkeystore -srckeystore "/etc/ssl/internal/server.p12" -alias 1 -srcstorepass ${KEYSTORE_PASSWORD} -srcstoretype PKCS12 -destkeystore "/etc/ssl/internal/etapi-sign-key-ca.jks" -deststorepass ${KEYSTORE_PASSWORD} -deststoretype JKS -destalias "etapi-sign-key-ca"
echo "KEYSTORE UPDATED"

echo "Step 2 Update Truststore"

mkdir -pv tmp/etc/ssl/internal

# Trust chain for internal services
echo ${TRUST_STORE_CERTIFICATE} | base64 -d > "tmp/etc/ssl/internal/Certificate.pem"
echo ${CALLBACK_CERTIFICATE} | base64 -d > "tmp/etc/ssl/internal/Certificate_callback.pem"

# Add to trust store
keytool -keystore "$JAVA_HOME/lib/security/cacerts" -storepass changeit -noprompt -trustcacerts -importcert -alias Certificate.pem -file "tmp/etc/ssl/internal/Certificate.pem"
keytool -keystore "$JAVA_HOME/lib/security/cacerts" -storepass changeit -noprompt -trustcacerts -importcert -alias Certificate_callback.pem -file "tmp/etc/ssl/internal/Certificate_callback.pem"

echo "TRUSTSTORE UPDATED"

# Run the application
java -javaagent:/opt/datadog/dd-java-agent.jar -Dcom.sun.management.jmxremote -cp @/app/jib-classpath-file ca.bnc.payment.Application
