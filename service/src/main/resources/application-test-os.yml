app:
  name: pmt-outgoing-domestic-payment-management
application:
  async:
    thread:
      pool:
        awaitTerminationSeconds: 60
        coreSize: 10
        maxSize: 10
  directParticipantIdentifier: CA000612
applicationmanagement:
  thresholdheapinpercent: 95
caching:
  spring:
    ttl: 43200000
dynamodb:
  tables:
    outgoing-payment:
      endpoint: http://localhost:8001
      region: ca-central-1
      tableName: OUTGOING_DOMESTIC_PAYMENT_TABLE_DEV
interac-security:
  enabled: false
  keystore-password: password
kafka:
  bootstrap-servers: localhost:9092
  password: ''
  retry-template:
    backoffPeriod: 200
    maxAttempts: 3
  sasl-mechanism: PLAIN
  security-protocol: PLAINTEXT
  topics:
    payment-notification: payment-notification-topic
    pptd: pptd-topic
  username: ''
logging:
  level:
    ca.bnc.payment: INFO
    ca.nbc.payment: INFO
    org.apache.kafka: INFO
okta:
  authorizationServerId: "authorizationServerId"
  enabled: true
  jwks:
    authorizationServerId: "jwksAuthorizationServerId"
    enabled: true
    url: "http://localhost:13001"
  url: "http://localhost:13001"
processing:
  allottedProcessingMaxElapsedTime: 20000
providers:
  bank-account-connector:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
  callback:
    connectTimeout: 3000
    readTimeout: 3000
    retry:
      attempts: 3
      backoff: 100
    whitelist: http://localhost:8081/outgoing-domestic-payment/callback
  domestic-creditors:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
  fraud:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
  interac-payments:
    expirationTime: 30
    fiAccountId: 612
    indirectConnectorId: *********
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
  limits-velocity:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
    x-version: v1
  party:
    retry:
      attempts: 3
      backoff: 100
    url: http://localhost:8081
server:
  port: 8090
spring:
  cloud:
    openfeign:
      client:
        config:
          bankAccountConnectorClient:
            connectTimeout: 2000
            readTimeout: 2000
          domesticCreditorsApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          fraudApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          interacPaymentsApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          limitsVelocityApiClient:
            connectTimeout: 2000
            readTimeout: 2000
          partyApiClient:
            connectTimeout: 2000
            readTimeout: 2000
      httpclient:
        disableSslValidation: true
      ssl:
        enabled: false
ssl:
  trust-store-password: "ab3c1fb96124b8b5896a933924c25a20aebe3aeca2ca7400e8a8a66a77c21ab9"
  trust-store: ${user.home}/pmt/certs/truststore.jks
