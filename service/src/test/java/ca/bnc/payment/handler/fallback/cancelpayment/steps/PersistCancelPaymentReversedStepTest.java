package ca.bnc.payment.handler.fallback.cancelpayment.steps;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.repository.OutgoingPaymentRepository;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PersistCancelPaymentReversedStepTest {

    private static final String CANCEL_PAYMENT_REVERSED_STATUS = "CANCEL_PAYMENT_REVERSED";

    private final Logger logger = (Logger) LoggerFactory.getLogger(PersistCancelPaymentReversedStep.class.getName());

    private PersistCancelPaymentReversedStep instance;
    @Mock
    private OutgoingPaymentRepository outgoingPaymentRepository;
    @Mock
    private LoggingFacade loggingFacade;

    @Mock
    private TaskContext<DomesticCancellationPaymentRequest> taskContext;
    @Mock
    private CancelPaymentContext cancelPaymentContext;
    @Mock
    private OutgoingDomesticPayment outgoingDomesticPayment;
    @Mock
    private OutgoingPaymentException outgoingPaymentException;

    @BeforeEach
    void setUp() {
        instance = new PersistCancelPaymentReversedStep(outgoingPaymentRepository, loggingFacade);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                outgoingPaymentRepository,
                loggingFacade,
                taskContext,
                cancelPaymentContext,
                outgoingDomesticPayment,
                outgoingPaymentException);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void whenIsApplicable_ThenReturnExpectedResult(final boolean value) {
        given(cancelPaymentContext.isDoSetPersistReversedStatus()).willReturn(value);

        final boolean result = instance.isApplicable(cancelPaymentContext);

        assertThat(result).isEqualTo(value);
    }

    @Test
    void givenNoErrorsDuringProcess_whenApply_ThenShouldUpdateDatabase() {
        given(cancelPaymentContext.getTaskContext()).willReturn(taskContext);
        given(taskContext.getOutgoingDomesticPayment()).willReturn(outgoingDomesticPayment);

        boolean result = instance.apply(cancelPaymentContext);

        InOrder inOrder = inOrder(taskContext, outgoingDomesticPayment, outgoingPaymentRepository);
        then(taskContext).should(inOrder).setProcessingStatus(CANCEL_PAYMENT_REVERSED_STATUS);
        then(outgoingDomesticPayment).should(inOrder).setProcessingStatus(CANCEL_PAYMENT_REVERSED_STATUS);
        then(outgoingPaymentRepository).should(inOrder).update(outgoingDomesticPayment);
        assertThat(result).isTrue();
    }

    @Test
    void givenExceptionOnUpdate_whenApply_thenSuppressedExceptionIsAdded() {
        final RuntimeException thrown = Instancio.create(RuntimeException.class);

        given(cancelPaymentContext.getTaskContext()).willReturn(taskContext);
        given(taskContext.getOutgoingDomesticPayment()).willReturn(outgoingDomesticPayment);
        willThrow(thrown)
                .given(outgoingPaymentRepository)
                .update(outgoingDomesticPayment);

        boolean result = instance.apply(cancelPaymentContext);

        InOrder inOrder = inOrder(taskContext, outgoingDomesticPayment, outgoingPaymentException, loggingFacade);
        then(taskContext).should(inOrder).setProcessingStatus(CANCEL_PAYMENT_REVERSED_STATUS);
        then(outgoingDomesticPayment).should(inOrder).setProcessingStatus(CANCEL_PAYMENT_REVERSED_STATUS);
        then(loggingFacade).should(inOrder).error(logger, thrown);
        assertThat(result).isFalse();
    }

}
