package ca.bnc.payment.handler.happypath.cancelpayment.steps;

import ca.bnc.payment.exception.interac.InteracException;
import ca.bnc.payment.exception.interac.InteracTimeoutException;
import ca.bnc.payment.exception.interac.RetryableInteracException;
import ca.bnc.payment.facade.InteracPaymentsFacade;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification;
import ca.bnc.payment.util.RequestDtoUtil;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_INTERAC_INITIATED_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.CANCEL_PAYMENT_INTERAC_INITIATING_STATUS;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracInitiateCancelStepTest {

    private static final String END_TO_END_IDENTIFICATION = "END_TO_END_ID";

    private InteracInitiateCancelStep testee;

    @Mock
    private InteracPaymentsFacade interacPaymentsFacade;
    @Mock
    private CancelPaymentContext cancelPaymentContext;
    @Mock
    private TaskContext<DomesticCancellationPaymentRequest> taskContext;
    @Mock
    private RequestDtoUtil requestDtoUtil;

    @BeforeEach
    void setUp() {
        testee = new InteracInitiateCancelStep(interacPaymentsFacade, requestDtoUtil);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                interacPaymentsFacade,
                cancelPaymentContext,
                taskContext,
                requestDtoUtil);
    }

    @Test
    void givenNoErrorsDuringProcess_whenApply_thenTransferCancelBegin() {
        given(cancelPaymentContext.getTaskContext()).willReturn(taskContext);

        testee.apply(cancelPaymentContext);

        then(taskContext).should().setProcessingStatus(CANCEL_PAYMENT_INTERAC_INITIATING_STATUS);
        then(interacPaymentsFacade).should().transferCancelBegin(cancelPaymentContext);
        then(taskContext).should().setProcessingStatus(CANCEL_PAYMENT_INTERAC_INITIATED_STATUS);
    }

    @Test
    void givenContext_whenStepThrowsInteracTimeoutException_thenContextUpdatedAndExceptionRethrown() {
        InteracTimeoutException exception = Instancio.create(InteracTimeoutException.class);
        RequestDto<DomesticCancellationPaymentRequest> requestDto = createRequestDto();

        given(cancelPaymentContext.getTaskContext()).willReturn(taskContext);
        given(taskContext.getRequestDto()).willReturn(requestDto);
        given(requestDtoUtil.getCancelEndToEndIdentification(requestDto)).willReturn(END_TO_END_IDENTIFICATION);
        willThrow(exception).given(interacPaymentsFacade).transferCancelBegin(cancelPaymentContext);

        assertThatThrownBy(() -> testee.apply(cancelPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(CANCEL_PAYMENT_INTERAC_INITIATING_STATUS);
        then(cancelPaymentContext).should().setCancelTxnId(END_TO_END_IDENTIFICATION);
        then(cancelPaymentContext).should().setDoCancelPaymentRollback(true);
        then(cancelPaymentContext).should().setDoSetPersistReversedStatus(true);
    }

    @ParameterizedTest
    @MethodSource("providerOtherInteracException")
    void givenContext_whenStepThrowsOtherInteracException_thenContextUpdatedAndExceptionRethrown(final Exception exception) {
        given(cancelPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(exception).given(interacPaymentsFacade).transferCancelBegin(cancelPaymentContext);

        assertThatThrownBy(() -> testee.apply(cancelPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(CANCEL_PAYMENT_INTERAC_INITIATING_STATUS);
        then(cancelPaymentContext).should().setDoSetPersistReversedStatus(true);
    }

    private static Stream<Arguments> providerOtherInteracException() {
        return Stream.of(
                Arguments.of(Instancio.create(InteracException.class)),
                Arguments.of(Instancio.create(RetryableInteracException.class))
        );
    }

    private RequestDto<DomesticCancellationPaymentRequest> createRequestDto() {
        return Instancio.of(new TypeToken<RequestDto<DomesticCancellationPaymentRequest>>() {
                })
                .set(field(PaymentIdentification::getEndToEndIdentification), END_TO_END_IDENTIFICATION)
                .create();
    }

}
