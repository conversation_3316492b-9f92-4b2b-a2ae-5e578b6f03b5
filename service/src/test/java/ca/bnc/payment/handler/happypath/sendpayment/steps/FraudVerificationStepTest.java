package ca.bnc.payment.handler.happypath.sendpayment.steps;

import ca.bnc.payment.exception.fraud.FraudException;
import ca.bnc.payment.facade.FraudFacade;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_fraud_resources.generated.model.FraudCheckResponse;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.util.ChannelIdUtil;
import ca.bnc.payment.util.ParticipantIdUtil;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static ca.bnc.payment.constant.FraudErrorCodes.FRAUDULENT_TRANSACTION;
import static ca.bnc.payment.constant.FraudErrorMessage.FRAUD_DECISION_NOT_CHECKED;
import static ca.bnc.payment.constant.FraudErrorMessage.FRAUD_REJECTED;
import static ca.bnc.payment.constant.PaymentStatus.SEND_PAYMENT_FRAUD_CHECKED_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.SEND_PAYMENT_FRAUD_CHECKING_STATUS;
import static ca.bnc.payment.constant.PaymentStatus.SEND_PAYMENT_FRAUD_NOT_CHECKED_STATUS;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class FraudVerificationStepTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(FraudVerificationStep.class.getName());

    public static final String REJECTED_EXCEPTION_MESSAGE = "FRAUDULENT_TRANSACTION - errors: [Error(code=FRAUDULENT_TRANSACTION, text=Transaction is refused by the domain fraud.)]";
    public static final String CHANNEL_ID = "channelId";

    @Mock
    private FraudFacade fraudFacade;
    @Mock
    private LoggingFacade loggingFacade;

    @Mock
    private TaskContext<SendPaymentRequest> taskContext;

    @Mock
    private ParticipantIdUtil participantIdUtil;
    @Mock
    private ChannelIdUtil channelIdUtil;

    private FraudVerificationStep testee;

    @BeforeEach
    void setup() {

        testee = new FraudVerificationStep(
                fraudFacade,
                loggingFacade,
                participantIdUtil,
                channelIdUtil
        );
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(
                fraudFacade,
                loggingFacade,
                taskContext,
                participantIdUtil,
                channelIdUtil);
    }

    @Test
    void doNotCallFraud_OsParticipantFalse_() {
        final SendPaymentContext sendPaymentContext = Instancio.create(SendPaymentContext.class);
        given(participantIdUtil.isOsParticipant()).willReturn(FALSE);
        assertDoesNotThrow(() -> testee.apply(sendPaymentContext));
    }

    @Test
    void doNotCallFraud_OsParticipantTrue_BneChannelTrue() {
        Instancio.create(FraudException.class);
        final SendPaymentContext sendPaymentContext = createSendPaymentContext();

        given(taskContext.getChannelID()).willReturn(CHANNEL_ID);
        given(participantIdUtil.isOsParticipant()).willReturn(TRUE);
        given(channelIdUtil.isBneChannelId(CHANNEL_ID)).willReturn(TRUE);
        assertDoesNotThrow(() -> testee.apply(sendPaymentContext));
    }

    @Test
    void givenException_whenDecode_thenLogError() {

        FraudException exception = Instancio.create(FraudException.class);
        final SendPaymentContext sendPaymentContext = createSendPaymentContext();

        given(participantIdUtil.isOsParticipant()).willReturn(TRUE);
        given(channelIdUtil.isBneChannelId(CHANNEL_ID)).willReturn(FALSE);
        given(taskContext.getChannelID()).willReturn(CHANNEL_ID);
        given(fraudFacade.fraudPreventionOnlineFraudVerificationPost(sendPaymentContext)).willThrow(exception);

        testee.apply(sendPaymentContext);
        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_CHECKING_STATUS);
        then(loggingFacade).should().error(LOGGER, exception);
        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_NOT_CHECKED_STATUS);
    }

    private SendPaymentContext createSendPaymentContext() {
        return Instancio.of(SendPaymentContext.class)
                .set(field(SendPaymentContext::getTaskContext), taskContext)
                .create();
    }

    @Test
    void givenFraudNotChecked_whenDecode_ContinueProcessing() {
        FraudCheckResponse fraudResponse = Instancio.create(FraudCheckResponse.class);
        fraudResponse.decision(FraudCheckResponse.DecisionEnum.FRAUD_NOT_CHECKED);

        final SendPaymentContext sendPaymentContext = createSendPaymentContext();

        given(participantIdUtil.isOsParticipant()).willReturn(TRUE);
        given(channelIdUtil.isBneChannelId(CHANNEL_ID)).willReturn(FALSE);

        given(taskContext.getChannelID()).willReturn(CHANNEL_ID);
        given(fraudFacade.fraudPreventionOnlineFraudVerificationPost(sendPaymentContext)).willReturn(fraudResponse);

        testee.apply(sendPaymentContext);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_CHECKING_STATUS);
        then(loggingFacade).should().warn(LOGGER, FRAUD_DECISION_NOT_CHECKED);
        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_NOT_CHECKED_STATUS);

    }

    @Test
    void givenAccepted_whenDecode_thenContinueProcessing() {

        FraudCheckResponse fraudResponse = Instancio.create(FraudCheckResponse.class);
        fraudResponse.decision(FraudCheckResponse.DecisionEnum.ACCEPTED);
        final SendPaymentContext sendPaymentContext = createSendPaymentContext();

        given(participantIdUtil.isOsParticipant()).willReturn(TRUE);
        given(channelIdUtil.isBneChannelId(CHANNEL_ID)).willReturn(FALSE);

        given(taskContext.getChannelID()).willReturn(CHANNEL_ID);
        given(fraudFacade.fraudPreventionOnlineFraudVerificationPost(sendPaymentContext)).willReturn(fraudResponse);

        testee.apply(sendPaymentContext);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_CHECKING_STATUS);
        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_CHECKED_STATUS);

    }

    @Test
    void givenRejected_whenDecode_thenThrowException() {

        final FraudCheckResponse fraudResponse = new FraudCheckResponse().decision(FraudCheckResponse.DecisionEnum.REJECTED);
        final SendPaymentContext sendPaymentContext = Instancio.of(SendPaymentContext.class)
                .set(field(SendPaymentContext::getTaskContext), taskContext)
                .set(field(SendPaymentContext::isDoPersistReverseStatus), FALSE)
                .set(field(SendPaymentContext::isDoStopInteracPayment), FALSE)
                .create();

        given(participantIdUtil.isOsParticipant()).willReturn(TRUE);
        given(channelIdUtil.isBneChannelId(CHANNEL_ID)).willReturn(FALSE);

        given(taskContext.getChannelID()).willReturn(CHANNEL_ID);
        given(fraudFacade.fraudPreventionOnlineFraudVerificationPost(sendPaymentContext)).willReturn(fraudResponse);

        FraudException fraudException = (FraudException) catchThrowable(() -> testee.apply(sendPaymentContext));
        assertThat(fraudException).isInstanceOf(FraudException.class);
        assertThat(fraudException.getMessage()).isEqualTo(REJECTED_EXCEPTION_MESSAGE);
        assertThat(fraudException.getErrors()).satisfies(errors -> assertThat(errors)
                .singleElement()
                .satisfies(error -> {
                    assertThat(error.code()).isEqualTo(FRAUDULENT_TRANSACTION);
                    assertThat(error.text()).isEqualTo(FRAUD_REJECTED);
                }));

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_FRAUD_CHECKING_STATUS);
        then(fraudFacade).should().fraudPreventionOnlineFraudVerificationPost(sendPaymentContext);

        assertThat(sendPaymentContext)
                .returns(TRUE, SendPaymentContext::isDoPersistReverseStatus)
                .returns(TRUE, SendPaymentContext::isDoStopInteracPayment)
                .returns(fraudException, SendPaymentContext::getOutgoingPaymentException);
    }
}
