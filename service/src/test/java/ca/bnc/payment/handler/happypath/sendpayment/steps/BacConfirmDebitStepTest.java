package ca.bnc.payment.handler.happypath.sendpayment.steps;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorException;
import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorTimeoutException;
import ca.bnc.payment.exception.bankaccountconnector.RetryableBankAccountConnectorException;
import ca.bnc.payment.facade.BankAccountConnectorFacade;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacConfirmDebitStepTest {

    private static final String SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMING_STATUS = "SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMING";
    private static final String SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMED_STATUS = "SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMED";

    private BacConfirmDebitStep instance;

    @Mock
    private BankAccountConnectorFacade bankAccountConnectorFacade;
    @Mock
    private SendPaymentContext sendPaymentContext;
    @Mock
    private TaskContext<SendPaymentRequest> taskContext;

    @BeforeEach
    void setUp() {
        instance = new BacConfirmDebitStep(bankAccountConnectorFacade);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                bankAccountConnectorFacade,
                sendPaymentContext,
                taskContext);
    }

    @Test
    void givenContext_whenApply_thenStepIsExecuted() {
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);

        instance.apply(sendPaymentContext);

        InOrder inOrder = inOrder(taskContext, bankAccountConnectorFacade);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMING_STATUS);
        then(bankAccountConnectorFacade).should(inOrder).confirmDebit(sendPaymentContext);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMED_STATUS);
    }

    @Test
    void givenContext_whenStepThrowsBacTimeoutException_thenContextUpdatedAndExceptionRethrown() {
        BankAccountConnectorTimeoutException exception = Instancio.create(BankAccountConnectorTimeoutException.class);
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(exception).given(bankAccountConnectorFacade).confirmDebit(sendPaymentContext);

        assertThatThrownBy(() -> instance.apply(sendPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMING_STATUS);
        then(sendPaymentContext).should().setDoPersistReverseStatus(true);
        then(sendPaymentContext).should().setDoReverseBankAccountDebit(true);
        then(sendPaymentContext).should().setDoStopInteracPayment(true);
        then(sendPaymentContext).should().setDoDecrementLimitVelocities(true);
        then(sendPaymentContext).should().setOutgoingPaymentException(exception);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("providerOtherBacException")
    void givenContext_whenStepThrowsOtherBacException_thenContextUpdatedAndExceptionRethrown(final OutgoingPaymentException exception) {
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(exception).given(bankAccountConnectorFacade).confirmDebit(sendPaymentContext);

        assertThatThrownBy(() -> instance.apply(sendPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_CONFIRMING_STATUS);
        then(sendPaymentContext).should().setDoPersistReverseStatus(true);
        then(sendPaymentContext).should().setDoStopInteracPayment(true);
        then(sendPaymentContext).should().setDoDecrementLimitVelocities(true);
        then(sendPaymentContext).should().setOutgoingPaymentException(exception);
    }

    private static Stream<Arguments> providerOtherBacException() {
        return Stream.of(
                argumentSet("BankAccountConnectorException", Instancio.create(BankAccountConnectorException.class)),
                argumentSet(
                        "RetryableBankAccountConnectorException",
                        Instancio.create(RetryableBankAccountConnectorException.class)));
    }

}
