package ca.bnc.payment.handler.happypath.sendpayment.steps;

import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorException;
import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorTimeoutException;
import ca.bnc.payment.exception.bankaccountconnector.RetryableBankAccountConnectorException;
import ca.bnc.payment.facade.BankAccountConnectorFacade;
import ca.bnc.payment.handler.happypath.sendpayment.steps.intermediate.StoreSendPaymentRequest;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.OriginalGroupInformation29;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacInitiateDebitStepTest {

    private static final String SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS = "SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING";
    private static final String SEND_PAYMENT_ACCOUNT_DEBIT_INITIATED_STATUS = "SEND_PAYMENT_ACCOUNT_DEBIT_INITIATED";
    private static final OriginalGroupInformation29 ORIGINAL_GROUP_INFORMATION = Instancio.create(OriginalGroupInformation29.class);

    private BacInitiateDebitStep instance;

    @Mock
    private StoreSendPaymentRequest storeSendPaymentRequest;
    @Mock
    private BankAccountConnectorFacade facade;
    @Mock
    private SendPaymentContext sendPaymentContext;
    @Mock
    private TaskContext<SendPaymentRequest> taskContext;

    @BeforeEach
    void setUp() {
        instance = new BacInitiateDebitStep(storeSendPaymentRequest, facade);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                storeSendPaymentRequest,
                facade,
                sendPaymentContext,
                taskContext);
    }

    @Test
    void givenContext_whenApply_thenStepIsExecuted() {
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        DebitResponse debitResponse = initiateDebitResponse();
        given(facade.initiateDebit(sendPaymentContext)).willReturn(debitResponse);

        instance.apply(sendPaymentContext);

        InOrder inOrder = inOrder(sendPaymentContext, taskContext, storeSendPaymentRequest, facade);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        then(storeSendPaymentRequest).should(inOrder).apply(sendPaymentContext, SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        then(facade).should(inOrder).initiateDebit(sendPaymentContext);
        then(sendPaymentContext).should(inOrder).setInitiateBankAccountDebitResponseOriginalGroupInfo(ORIGINAL_GROUP_INFORMATION);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_INITIATED_STATUS);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideBacException")
    void givenContext_whenStepThrowsBacException_thenContextUpdatedAndExceptionRethrown(final Exception exception) {
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(exception).given(facade).initiateDebit(sendPaymentContext);

        assertThatThrownBy(() -> instance.apply(sendPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        then(storeSendPaymentRequest).should().apply(sendPaymentContext, SEND_PAYMENT_ACCOUNT_DEBIT_INITIATING_STATUS);
        then(sendPaymentContext).should().setDoPersistReverseStatus(true);
        then(sendPaymentContext).should().setOutgoingPaymentException((BankAccountConnectorException) exception);
    }

    private static Stream<Arguments> provideBacException() {
        return Stream.of(
                argumentSet("BankAccountConnectorTimeoutException", Instancio.create(BankAccountConnectorTimeoutException.class)),
                argumentSet("BankAccountConnectorException", Instancio.create(BankAccountConnectorException.class)),
                argumentSet("RetryableBankAccountConnectorException", Instancio.create(RetryableBankAccountConnectorException.class))
        );
    }

    private DebitResponse initiateDebitResponse() {
        DebitResponse debitResponse = Instancio.create(DebitResponse.class);
        debitResponse
                .getFiToFiPaymentStatusReport()
                .getTransactionInformationAndStatus()
                .get(0)
                .setOriginalGroupInformation(ORIGINAL_GROUP_INFORMATION);
        return debitResponse;
    }
}
