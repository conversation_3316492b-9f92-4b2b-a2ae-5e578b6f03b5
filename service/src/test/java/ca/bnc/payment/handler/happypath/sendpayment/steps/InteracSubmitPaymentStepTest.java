package ca.bnc.payment.handler.happypath.sendpayment.steps;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.interac.InteracException;
import ca.bnc.payment.exception.interac.InteracTimeoutException;
import ca.bnc.payment.exception.interac.RetryableInteracException;
import ca.bnc.payment.facade.InteracPaymentsFacade;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracSubmitPaymentStepTest {

    private static final String SEND_PAYMENT_INTERAC_SUBMITTING_STATUS = "SEND_PAYMENT_INTERAC_SUBMITTING";
    private static final String SEND_PAYMENT_COMPLETED_STATUS = "SEND_PAYMENT_COMPLETED";
    private static final String INTERAC_TRANSACTION_ALREADY_SUBMITTED_ERROR_CODE = "205";

    private InteracSubmitPaymentStep instance;
    @Mock
    private InteracPaymentsFacade interacPaymentsFacade;

    @Mock
    private SendPaymentContext sendPaymentContext;
    @Mock
    private TaskContext<SendPaymentRequest> taskContext;

    @BeforeEach
    void setUp() {
        instance = new InteracSubmitPaymentStep(interacPaymentsFacade);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                interacPaymentsFacade,
                sendPaymentContext,
                taskContext);
    }

    @Test
    void givenContext_whenApply_thenStepIsExecuted() {
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);

        instance.apply(sendPaymentContext);

        InOrder inOrder = inOrder(taskContext, interacPaymentsFacade, sendPaymentContext);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_INTERAC_SUBMITTING_STATUS);
        then(interacPaymentsFacade).should(inOrder).submitPayment(sendPaymentContext);
        then(sendPaymentContext).should(inOrder).setSendPaymentCompleted(true);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_COMPLETED_STATUS);
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("provideInteracException")
    void givenContext_whenStepThrowsInteracException_thenContextUpdatedAndExceptionRethrown(final OutgoingPaymentException exception) {

        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(exception).given(interacPaymentsFacade).submitPayment(sendPaymentContext);

        assertThatThrownBy(() -> instance.apply(sendPaymentContext))
                .isSameAs(exception);

        then(taskContext).should().setProcessingStatus(SEND_PAYMENT_INTERAC_SUBMITTING_STATUS);
        then(interacPaymentsFacade).should().submitPayment(sendPaymentContext);
        then(sendPaymentContext).should().setDoPersistReverseStatus(true);
        then(sendPaymentContext).should().setDoReverseBankAccountDebit(true);
        then(sendPaymentContext).should().setDoStopInteracPayment(true);
        then(sendPaymentContext).should().setDoDecrementLimitVelocities(true);
        then(sendPaymentContext).should().setOutgoingPaymentException(exception);
    }

    private static Stream<Arguments> provideInteracException() {
        return Stream.of(
                argumentSet("InteracTimeoutException", Instancio.create(InteracTimeoutException.class)),
                argumentSet("InteracException", Instancio.create(InteracException.class)),
                argumentSet("RetryableInteracException", Instancio.create(RetryableInteracException.class))
        );
    }

    @Test
    void givenInteracExceptionWithCode205_whenApply_thenResumeHappyPath() {
        InteracException interacException = new InteracException("Transaction already submitted",
                new OutgoingPaymentException.Error(INTERAC_TRANSACTION_ALREADY_SUBMITTED_ERROR_CODE, "Already submitted"));

        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(interacException).given(interacPaymentsFacade).submitPayment(sendPaymentContext);

        instance.apply(sendPaymentContext);

        InOrder inOrder = inOrder(taskContext, interacPaymentsFacade, sendPaymentContext);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_INTERAC_SUBMITTING_STATUS);
        then(interacPaymentsFacade).should(inOrder).submitPayment(sendPaymentContext);
        then(sendPaymentContext).should(inOrder).setSendPaymentCompleted(true);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_COMPLETED_STATUS);
    }

    @Test
    void givenInteracExceptionWithEmptyErrors_whenApply_thenExceptionIsHandled() {
        InteracException interacException = new InteracException("Interac exception");

        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        willThrow(interacException).given(interacPaymentsFacade).submitPayment(sendPaymentContext);

        assertThatThrownBy(() -> instance.apply(sendPaymentContext))
                .isSameAs(interacException);

        InOrder inOrder = inOrder(taskContext, interacPaymentsFacade, sendPaymentContext);
        then(taskContext).should(inOrder).setProcessingStatus(SEND_PAYMENT_INTERAC_SUBMITTING_STATUS);
        then(interacPaymentsFacade).should(inOrder).submitPayment(sendPaymentContext);
        then(sendPaymentContext).should(inOrder).setDoPersistReverseStatus(true);
        then(sendPaymentContext).should(inOrder).setDoReverseBankAccountDebit(true);
        then(sendPaymentContext).should(inOrder).setDoStopInteracPayment(true);
        then(sendPaymentContext).should(inOrder).setDoDecrementLimitVelocities(true);
        then(sendPaymentContext).should(inOrder).setOutgoingPaymentException(interacException);
    }

}
