package ca.bnc.payment.controller;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticPaymentNotificationRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.UUID;

import static ca.bnc.payment.controller.LogContextHelper.LOG_FIELD_CLIENT_ID;
import static ca.bnc.payment.controller.LogContextHelper.LOG_FIELD_END_TO_END_IDENTIFICATION;
import static ca.bnc.payment.controller.LogContextHelper.LOG_FIELD_PROCESSING_STATUS;
import static ca.bnc.payment.controller.LogContextHelper.LOG_FIELD_REQUEST_ID;
import static ca.bnc.payment.controller.LogContextHelper.PROCESSING_STATUS_CANCEL_PAYMENT_INITIATED;
import static ca.bnc.payment.controller.LogContextHelper.PROCESSING_STATUS_SEND_PAYMENT_INITIATED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class LogContextHelperTest {

    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final UUID REQUEST_ID = Instancio.create(UUID.class);
    private static final String CLIENT_ID = Instancio.create(String.class);

    private LogContextHelper testee;

    @BeforeEach
    void setup() {
        testee = new LogContextHelper();
    }

    @Test
    void testContextForDomesticCancellationPaymentRequest() {

        DomesticCancellationPaymentRequest request = Instancio.of(DomesticCancellationPaymentRequest.class)
                .set(field(PaymentIdentification::getEndToEndIdentification), END_TO_END_IDENTIFICATION)
                .create();

        Map<String, Object> actual = testee.contextFor(REQUEST_ID, CLIENT_ID, request);

        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                LOG_FIELD_REQUEST_ID, REQUEST_ID.toString(),
                LOG_FIELD_CLIENT_ID, CLIENT_ID,
                LOG_FIELD_END_TO_END_IDENTIFICATION, END_TO_END_IDENTIFICATION,
                LOG_FIELD_PROCESSING_STATUS, PROCESSING_STATUS_CANCEL_PAYMENT_INITIATED));
    }

    @Test
    void testContextForSendPaymentRequest() {

        SendPaymentRequest request = Instancio.of(SendPaymentRequest.class)
                .set(field(PaymentIdentification7::getEndToEndIdentification), END_TO_END_IDENTIFICATION)
                .create();

        Map<String, Object> actual = testee.contextFor(REQUEST_ID, CLIENT_ID, request);

        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                LOG_FIELD_REQUEST_ID, REQUEST_ID.toString(),
                LOG_FIELD_CLIENT_ID, CLIENT_ID,
                LOG_FIELD_END_TO_END_IDENTIFICATION, END_TO_END_IDENTIFICATION,
                LOG_FIELD_PROCESSING_STATUS, PROCESSING_STATUS_SEND_PAYMENT_INITIATED));
    }

    @Test
    void testContextForReissuePaymentRequest() {

        DomesticPaymentNotificationRequest request = Instancio.of(DomesticPaymentNotificationRequest.class)
                .set(field(DomesticPaymentNotificationRequest::getEndToEndIdentification), END_TO_END_IDENTIFICATION)
                .create();

        Map<String, Object> actual = testee.contextFor(REQUEST_ID, CLIENT_ID, request);

        assertThat(actual).containsExactlyInAnyOrderEntriesOf(Map.of(
                LOG_FIELD_REQUEST_ID, REQUEST_ID.toString(),
                LOG_FIELD_CLIENT_ID, CLIENT_ID,
                LOG_FIELD_END_TO_END_IDENTIFICATION, END_TO_END_IDENTIFICATION));
    }

}
