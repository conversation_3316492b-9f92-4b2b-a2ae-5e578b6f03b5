package ca.bnc.payment.config;

import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Map;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.params.ParameterizedTest.ARGUMENT_SET_NAME_PLACEHOLDER;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class KafkaConfigTest {
    private static final Logger LOGGER = (Logger) LoggerFactory.getLogger(KafkaConfig.class.getName());
    public static final String BOOSTRAP_SERVERS = "localhost:9092";
    public static final String KAFKA_PLAIN_MODULE = "org.apache.kafka.common.security.plain.PlainLoginModule";
    public static final String KAFKA_SCRAM_MODULE = "org.apache.kafka.common.security.scram.ScramLoginModule";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Mock
    private LoggingFacade loggingFacade;

    private KafkaConfig kafkaConfig;


    @BeforeEach
    void setUp() {
        kafkaConfig = new KafkaConfig();
    }

    @AfterEach
    public void tearDown() {
        verifyNoMoreInteractions(loggingFacade);
    }

    @ParameterizedTest(name = "Security Protocol - {0}")
    @ValueSource(strings = {"PLAINTEXT", "SASL_SSL", "ANY_OTHER_STRING"})
    void shouldReturn_KafkaTemplate_WithoutJaasConfig(String securityProtocol) {

        final KafkaConfigProperties kafkaConfigProperties = Instancio.of(KafkaConfigProperties.class)
                .set(field(KafkaConfigProperties::bootstrapServers), BOOSTRAP_SERVERS)
                .set(field(KafkaConfigProperties::securityProtocol), securityProtocol)
                .create();

        KafkaTemplate<String, Object> kafkaTemplate = kafkaConfig.kafkaTemplate(kafkaConfigProperties, loggingFacade, OBJECT_MAPPER);

        ProducerFactory<String, Object> producerFactory = kafkaTemplate.getProducerFactory();

        Map<String, Object> configs = producerFactory.getConfigurationProperties();

        Map<String, Object> expectedConfigs = Map.of(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOSTRAP_SERVERS,
                CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol,
                SaslConfigs.SASL_MECHANISM, kafkaConfigProperties.saslMechanism()
        );

        assertThat(configs)
                .usingRecursiveComparison()
                .isEqualTo(expectedConfigs);

        then(loggingFacade)
                .should()
                .info(LOGGER, "Kafka BOOTSTRAP_SERVERS: localhost:9092");
    }

    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("saslMechanismAndloginModuleProvider")
    void shouldTest_LoginCredentials_withQuotes(final String saslMechanism, final String loginModuleName, final String loginModule) {

        final KafkaConfigProperties kafkaConfigProperties = Instancio.of(KafkaConfigProperties.class)
                .set(field(KafkaConfigProperties::bootstrapServers), BOOSTRAP_SERVERS)
                .set(field(KafkaConfigProperties::securityProtocol), "SASL_SSL")
                .set(field(KafkaConfigProperties::saslMechanism), saslMechanism)
                .create();

        KafkaTemplate<String, Object> kafkaTemplate = kafkaConfig.kafkaTemplate(kafkaConfigProperties, loggingFacade, OBJECT_MAPPER);

        ProducerFactory<String, Object> producerFactory = kafkaTemplate.getProducerFactory();
        Map<String, Object> configs = producerFactory.getConfigurationProperties();

        String expectedJaasConfig = getJaasConfig(loginModule, kafkaConfigProperties.username(), kafkaConfigProperties.password());

        Map<String, Object> expectedConfigs = Map.of(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOSTRAP_SERVERS,
                CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL",
                SaslConfigs.SASL_MECHANISM, saslMechanism,
                SaslConfigs.SASL_JAAS_CONFIG, expectedJaasConfig
        );

        assertThat(configs)
                .usingRecursiveComparison()
                .isEqualTo(expectedConfigs);

        then(loggingFacade)
                .should()
                .info(LOGGER, "Configure KafkaTemplate with %s login module - %s".formatted(loginModuleName, loginModule));

        then(loggingFacade)
                .should()
                .info(LOGGER, "Kafka BOOTSTRAP_SERVERS: localhost:9092");
    }

    private static String getJaasConfig(final String loginModule, final String username, final String password) {
        return "%s required username='%s' password='%s';".formatted(loginModule, username, password);
    }


    @ParameterizedTest(name = ARGUMENT_SET_NAME_PLACEHOLDER)
    @MethodSource("usernameAndPasswordProvider")
    void shouldTestKafkaTemplateCredentials_WithSimpleQuotes(final String username,
                                                             final String password,
                                                             final String escapedUsername,
                                                             final String escapedPassword) {

        final KafkaConfigProperties kafkaConfigProperties = Instancio.of(KafkaConfigProperties.class)
                .set(field(KafkaConfigProperties::bootstrapServers), BOOSTRAP_SERVERS)
                .set(field(KafkaConfigProperties::securityProtocol), "SASL_SSL")
                .set(field(KafkaConfigProperties::saslMechanism), "SCRAM-SHA-512")
                .set(field(KafkaConfigProperties::username), username)
                .set(field(KafkaConfigProperties::password), password)
                .create();

        KafkaTemplate<String, Object> kafkaTemplate = kafkaConfig.kafkaTemplate(kafkaConfigProperties, loggingFacade, OBJECT_MAPPER);

        ProducerFactory<String, Object> producerFactory = kafkaTemplate.getProducerFactory();

        Map<String, Object> configs = producerFactory.getConfigurationProperties();

        String expectedJaasConfig = getJaasConfig("org.apache.kafka.common.security.scram.ScramLoginModule",
                escapedUsername, escapedPassword);

        Map<String, Object> expectedConfigs = Map.of(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOSTRAP_SERVERS,
                CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL",
                SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512",
                SaslConfigs.SASL_JAAS_CONFIG, expectedJaasConfig
        );

        assertThat(configs)
                .usingRecursiveComparison()
                .isEqualTo(expectedConfigs);

        then(loggingFacade)
                .should()
                .info(LOGGER, "Configure KafkaTemplate with Scram login module - org.apache.kafka.common.security.scram.ScramLoginModule");

        then(loggingFacade)
                .should()
                .info(LOGGER, "Kafka BOOTSTRAP_SERVERS: localhost:9092");
    }

    private static Stream<Arguments> usernameAndPasswordProvider() {
        return Stream.of(
                Arguments.argumentSet("Username and password with no single quotes with Scram Config",
                        "toto", "pll7383", "toto", "pll7383"),
                Arguments.argumentSet("Username with single quotes with Plain Config", "'to'to'", "pll7383", "\\'to\\'to\\'", "pll7383"),
                Arguments.argumentSet("Password with single quotes", "toto", "'pll7383'", "toto", "\\'pll7383\\'"),
                Arguments.argumentSet("Username and Password with single quotes", "'toto'", "p'll738'3", "\\'toto\\'", "p\\'ll738\\'3")
        );
    }

    private static Stream<Arguments> saslMechanismAndloginModuleProvider() {
        return Stream.of(
                Arguments.argumentSet("PLAIN Sasl Mechanism with uppercase letters", "PLAIN", "Plain", KAFKA_PLAIN_MODULE),
                Arguments.argumentSet("Plain Sasl Mechanism with mixed case letters", "Plain", "Plain", KAFKA_PLAIN_MODULE),
                Arguments.argumentSet("SCRAM-SHA-512 Sasl Mechanism with uppercase letters", "SCRAM-SHA-512",
                        "Scram", KAFKA_SCRAM_MODULE),
                Arguments.argumentSet("Scram-sha-512 Sasl Mechanism with mixed case letters", "Scram-sha-512",
                        "Scram", KAFKA_SCRAM_MODULE)
        );
    }

}