package ca.bnc.payment.adapter;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorException;
import ca.bnc.payment.util.FeignClientUtil;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BankAccountAdapterExceptionHandlerTest {

    private static final String ERROR_MESSAGE = Instancio.create(String.class);
    private static final String BANK_ACCOUNT_CONNECTOR_TIMEOUT = "The Bank Account Connector API is unreachable";
    private static final String BANK_ACCOUNT_CONNECTOR_GENERAL_EXCEPTION = "Exception calling Bank Account Connector, returned N/A";

    private BankAccountAdapterExceptionHandler bankAccountAdapterExceptionHandler;
    @Mock
    private FeignClientUtil feignClientUtil;

    @BeforeEach
    void setUp() {
        bankAccountAdapterExceptionHandler = new BankAccountAdapterExceptionHandler(feignClientUtil);
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(feignClientUtil);
    }

    @Test
    void givenOutgoingPaymentException_whenCatchException_thenAddE2EToException() {
        BankAccountConnectorException exception = new BankAccountConnectorException(ERROR_MESSAGE);

        OutgoingPaymentException result = bankAccountAdapterExceptionHandler.catchException(exception);

        assertThat(result.getMessage()).isEqualTo(ERROR_MESSAGE);
    }

    @Test
    void givenRetryableExceptionWithSocketCause_whenCatchException_thenMessageAPIUnreachable() {
        RetryableException exception = Instancio.create(RetryableException.class);
        given(feignClientUtil.isSocketTimeout(exception)).willReturn(Boolean.TRUE);

        OutgoingPaymentException result = bankAccountAdapterExceptionHandler.catchException(exception);

        assertThat(result.getMessage()).isEqualTo(BANK_ACCOUNT_CONNECTOR_TIMEOUT);
        assertThat(result.getCause()).isEqualTo(exception);
    }

    @Test
    void givenRuntimeException_whenCatchException_thenMessageGeneral() {
        RuntimeException exception = Instancio.create(RuntimeException.class);
        given(feignClientUtil.isSocketTimeout(exception)).willReturn(Boolean.FALSE);

        OutgoingPaymentException result = bankAccountAdapterExceptionHandler.catchException(exception);

        assertThat(result.getMessage()).isEqualTo(BANK_ACCOUNT_CONNECTOR_GENERAL_EXCEPTION);
        assertThat(result.getCause()).isEqualTo(exception);
    }
}
