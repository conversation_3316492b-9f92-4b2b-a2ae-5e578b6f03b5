package ca.bnc.payment.adapter;

import ca.bnc.payment.client.account.BankAccountConnectorClient;
import ca.bnc.payment.exception.bankaccountconnector.BankAccountConnectorException;
import ca.bnc.payment.model.dto.BankAccountConnectorDto;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Action;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChannelType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ConfirmCreditRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ConfirmCreditResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ConfirmDebitRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ConfirmDebitResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CreditRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CreditResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.DebitResponse;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentType;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ReverseRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ReverseResponse;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BankAccountAdapterTest {

    private static final String REGULAR_PAYMENT = "REGULAR_PAYMENT";
    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final BankAccountConnectorDto BANK_ACCOUNT_CONNECTOR_DTO = Instancio.create(BankAccountConnectorDto.class);
    private static final RuntimeException RUNTIME_EXCEPTION = Instancio.create(RuntimeException.class);
    private static final BankAccountConnectorException BANK_ACCOUNT_CONNECTOR_EXCEPTION =
            Instancio.create(BankAccountConnectorException.class);
    private static final RequestDto<SendPaymentRequest> SEND_REQUEST_DTO =
            Instancio.of(new TypeToken<RequestDto<SendPaymentRequest>>() {
            }).create();
    private static final RequestDto<DomesticCancellationPaymentRequest> CANCEL_REQUEST_DTO =
            Instancio.of(new TypeToken<RequestDto<DomesticCancellationPaymentRequest>>() {
            }).create();

    BankAccountAdapter bankAccountAdapter;

    @Mock
    BankAccountConnectorClient bankAccountConnectorClient;
    @Mock
    BankAccountAdapterExceptionHandler exceptionHandler;

    @BeforeEach
    void setUp() {
        bankAccountAdapter = new BankAccountAdapter(bankAccountConnectorClient, exceptionHandler);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(bankAccountConnectorClient, exceptionHandler);
    }

    @Test
    void whenInitiateDebit_thenReturnDebitResponse() {

        final DebitRequest debitRequest = Instancio.create(DebitRequest.class);
        final DebitResponse debitResponse = Instancio.create(DebitResponse.class);
        given(bankAccountConnectorClient.initiateDebit(
                SEND_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(SEND_REQUEST_DTO.getXChannelType().getValue()),
                Action.SEND,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                debitRequest)
        ).willReturn(ResponseEntity.ok(debitResponse));

        final DebitResponse actual = bankAccountAdapter.initiateDebit(
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO,
                debitRequest,
                SEND_REQUEST_DTO,
                REGULAR_PAYMENT);

        assertThat(actual).isSameAs(debitResponse);

    }

    @Test
    void givenException_whenInitiateDebit_ThenThrowException() {

        final DebitRequest debitRequest = Instancio.create(DebitRequest.class);
        given(bankAccountConnectorClient.initiateDebit(
                SEND_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(SEND_REQUEST_DTO.getXChannelType().getValue()),
                Action.SEND,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                debitRequest)
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() -> bankAccountAdapter.initiateDebit(
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO,
                debitRequest,
                SEND_REQUEST_DTO,
                REGULAR_PAYMENT)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

    @Test
    void whenReverseDebit_thenCallDebitReverse() {

        final ReverseRequest reverseRequest = Instancio.create(ReverseRequest.class);
        final ReverseResponse reverseResponse = Instancio.create(ReverseResponse.class);
        given(bankAccountConnectorClient.debitReverse(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                reverseRequest)
        ).willReturn(ResponseEntity.ok(reverseResponse));

        ReverseResponse actual = bankAccountAdapter.reverseDebit(END_TO_END_IDENTIFICATION, BANK_ACCOUNT_CONNECTOR_DTO, reverseRequest);

        assertThat(actual).isSameAs(reverseResponse);

    }

    @Test
    void givenException_whenReverseDebit_ThenThrowException() {

        final ReverseRequest reverseRequest = Instancio.create(ReverseRequest.class);
        given(bankAccountConnectorClient.debitReverse(BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                reverseRequest)
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() ->
                bankAccountAdapter.reverseDebit(END_TO_END_IDENTIFICATION, BANK_ACCOUNT_CONNECTOR_DTO, reverseRequest)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

    @Test
    void whenConfirmDebit_thenCallConfirmDebit() {

        final ConfirmDebitRequest confirmDebitRequest = Instancio.create(ConfirmDebitRequest.class);
        final ConfirmDebitResponse confirmDebitResponse = Instancio.create(ConfirmDebitResponse.class);
        given(bankAccountConnectorClient.confirmDebit(
                SEND_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(SEND_REQUEST_DTO.getXChannelType().getValue()),
                Action.SEND,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                confirmDebitRequest)
        ).willReturn(ResponseEntity.ok(confirmDebitResponse));

        ConfirmDebitResponse actual = bankAccountAdapter.confirmDebit(
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO,
                confirmDebitRequest,
                SEND_REQUEST_DTO,
                REGULAR_PAYMENT);

        assertThat(actual).isSameAs(confirmDebitResponse);

    }

    @Test
    void givenException_whenConfirmDebit_ThenThrowException() {

        final ConfirmDebitRequest confirmDebitRequest = Instancio.create(ConfirmDebitRequest.class);
        given(bankAccountConnectorClient.confirmDebit(
                SEND_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(SEND_REQUEST_DTO.getXChannelType().getValue()),
                Action.SEND,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                confirmDebitRequest)
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() -> bankAccountAdapter.confirmDebit(
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO,
                confirmDebitRequest,
                SEND_REQUEST_DTO,
                REGULAR_PAYMENT)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

    @Test
    void whenInitiateCredit_thenCallInitiateCredit() {

        final CreditRequest creditRequest = Instancio.create(CreditRequest.class);
        final CreditResponse creditResponse = Instancio.create(CreditResponse.class);
        given(bankAccountConnectorClient.initiateCredit(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                CANCEL_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(CANCEL_REQUEST_DTO.getXChannelType().getValue()),
                Action.CANCEL,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                creditRequest)
        ).willReturn(ResponseEntity.ok(creditResponse));

        CreditResponse actual = bankAccountAdapter.initiateCredit(
                END_TO_END_IDENTIFICATION,
                creditRequest,
                BANK_ACCOUNT_CONNECTOR_DTO,
                CANCEL_REQUEST_DTO);

        assertThat(actual).isSameAs(creditResponse);
    }

    @Test
    void givenException_whenInitiateCredit_ThenThrowException() {
        final CreditRequest creditRequest = Instancio.create(CreditRequest.class);
        given(bankAccountConnectorClient.initiateCredit(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                CANCEL_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(CANCEL_REQUEST_DTO.getXChannelType().getValue()),
                Action.CANCEL,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                creditRequest)
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() ->
                bankAccountAdapter.initiateCredit(END_TO_END_IDENTIFICATION, creditRequest, BANK_ACCOUNT_CONNECTOR_DTO, CANCEL_REQUEST_DTO)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

    @Test
    void whenCreditReverse_thenCallReverseCredit() {

        final ReverseRequest reverseRequest = Instancio.create(ReverseRequest.class);
        final ReverseResponse reverseResponse = Instancio.create(ReverseResponse.class);
        given(bankAccountConnectorClient.creditReverse(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                reverseRequest)
        ).willReturn(ResponseEntity.ok(reverseResponse));

        final ReverseResponse actual = bankAccountAdapter.reverseCredit(END_TO_END_IDENTIFICATION, reverseRequest, BANK_ACCOUNT_CONNECTOR_DTO);

        assertThat(actual).isSameAs(reverseResponse);

    }

    @Test
    void givenException_whenReverseCredit_ThenThrowException() {

        final ReverseRequest reverseRequest = Instancio.create(ReverseRequest.class);
        given((bankAccountConnectorClient.creditReverse(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                reverseRequest))
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() ->
                bankAccountAdapter.reverseCredit(END_TO_END_IDENTIFICATION, reverseRequest, BANK_ACCOUNT_CONNECTOR_DTO)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

    @Test
    void whenConfirmCredit_thenCallConfirmCredit() {

        final ConfirmCreditRequest confirmCreditRequest = Instancio.create(ConfirmCreditRequest.class);
        final ConfirmCreditResponse confirmCreditResponse = Instancio.create(ConfirmCreditResponse.class);
        given(bankAccountConnectorClient.confirmCredit(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                CANCEL_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(CANCEL_REQUEST_DTO.getXChannelType().getValue()),
                Action.CANCEL,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                confirmCreditRequest)
        ).willReturn(ResponseEntity.ok(confirmCreditResponse));

        ConfirmCreditResponse actual = bankAccountAdapter.confirmCredit(
                END_TO_END_IDENTIFICATION,
                confirmCreditRequest,
                BANK_ACCOUNT_CONNECTOR_DTO,
                CANCEL_REQUEST_DTO);

        assertThat(actual).isSameAs(confirmCreditResponse);

    }

    @Test
    void givenException_whenConfirmCredit_ThenThrowException() {

        final ConfirmCreditRequest confirmCreditRequest = Instancio.create(ConfirmCreditRequest.class);
        given((bankAccountConnectorClient.confirmCredit(
                BANK_ACCOUNT_CONNECTOR_DTO.getClientId(),
                CANCEL_REQUEST_DTO.getXChannelId(),
                ChannelType.fromValue(CANCEL_REQUEST_DTO.getXChannelType().getValue()),
                Action.CANCEL,
                PaymentType.REGULAR_PAYMENT,
                BANK_ACCOUNT_CONNECTOR_DTO.getRetryIndicator(),
                END_TO_END_IDENTIFICATION,
                BANK_ACCOUNT_CONNECTOR_DTO.getRequestId(),
                confirmCreditRequest))
        ).willThrow(RUNTIME_EXCEPTION);
        given(exceptionHandler.catchException(RUNTIME_EXCEPTION)).willReturn(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

        assertThatThrownBy(() ->
                bankAccountAdapter.confirmCredit(END_TO_END_IDENTIFICATION, confirmCreditRequest, BANK_ACCOUNT_CONNECTOR_DTO, CANCEL_REQUEST_DTO)
        ).isSameAs(BANK_ACCOUNT_CONNECTOR_EXCEPTION);

    }

}
