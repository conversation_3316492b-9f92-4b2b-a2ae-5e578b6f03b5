package ca.bnc.payment.adapter;

import ca.bnc.payment.client.interac.InteracPaymentsApiClient;
import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.interac.InteracException;
import ca.bnc.payment.exception.interac.InteracTimeoutException;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.AccountAliasType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.CancelPaymentTransaction;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ChannelIndicator;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentOptionResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPostRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPutRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ProductCode;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ReversePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SendTransferNotice;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SignatureType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentResponse;
import ca.bnc.payment.util.FeignClientUtil;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.catchThrowable;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracPaymentsAdapterTest {

    private static final String PARTICIPANT_ID = Instancio.create(String.class);
    private static final String PARTICIPANT_USER_ID = Instancio.create(String.class);
    private static final String REQUEST_ID = Instancio.create(String.class);
    private static final ChannelIndicator CHANNEL_INDICATOR = Instancio.create(ChannelIndicator.class);
    private static final String API_SIGNATURE = Instancio.create(String.class);
    private static final SignatureType API_SIGNATURE_TYPE = Instancio.create(SignatureType.class);
    private static final OffsetDateTime TRANSACTION_TIME = OffsetDateTime.now();
    private static final String RETRY_INDICATOR = Instancio.create(String.class);
    private static final String PAYMENT_TRANSACTION_TOKEN = Instancio.create(String.class);
    private static final String INDIRECT_CONNECTOR_ID = Instancio.create(String.class);
    private static final String AUTHORISATION = Instancio.create(String.class);
    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final String PARTICIPANT_CANCEL_TXN_TOKEN = Instancio.create(String.class);
    private static final String ETRANSFER_ID = Instancio.create(String.class);
    private static final String CANCEL_TRX_ID = Instancio.create(String.class);
    private static final String PARTICIPANT_TRANSACTION_ID = Instancio.create(String.class);
    private static final String PHONE_NUMBER = Instancio.create(String.class);

    private InteracPaymentsAdapter instance;
    @Mock
    private InteracPaymentsApiClient interacPaymentsApiClient;
    @Mock
    private FeignClientUtil feignClientUtil;

    @BeforeEach
    void setUp() {
        instance = new InteracPaymentsAdapter(interacPaymentsApiClient, feignClientUtil);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(interacPaymentsApiClient, feignClientUtil);
    }

    @Test
    void givenSuccessScenario_whenInitiatePayment_thenResponseReturned() {
        final InitiatePaymentRequest request = Instancio.create(InitiatePaymentRequest.class);
        final InitiatePaymentResponse response = Instancio.create(InitiatePaymentResponse.class);
        given(interacPaymentsApiClient.initiatePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willReturn(new ResponseEntity<>(response, HttpStatus.OK));

        final InitiatePaymentResponse actualResponse = instance.initiatePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        );

        assertThat(actualResponse).isEqualTo(response);
    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenInitiatePayment_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        final InitiatePaymentRequest request = Instancio.create(InitiatePaymentRequest.class);
        given(interacPaymentsApiClient.initiatePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue)
        );

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.initiatePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        ));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenReversePayment_thenResponseReturned() {
        final ReversePaymentResponse response = Instancio.create(ReversePaymentResponse.class);
        given(interacPaymentsApiClient.reversePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                PARTICIPANT_TRANSACTION_ID)
        ).willReturn(new ResponseEntity<>(response, HttpStatus.OK));

        final ReversePaymentResponse actualResponse = instance.reversePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                PARTICIPANT_TRANSACTION_ID,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        );

        assertThat(actualResponse).isEqualTo(response);
    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenReversePayment_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        given(interacPaymentsApiClient.reversePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                PARTICIPANT_TRANSACTION_ID)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue));

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.reversePayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                PARTICIPANT_TRANSACTION_ID,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        ));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenSubmitPayment_thenResponseReturned() {
        final SubmitPaymentRequest request = Instancio.create(SubmitPaymentRequest.class);
        final SubmitPaymentResponse response = Instancio.create(SubmitPaymentResponse.class);
        given(interacPaymentsApiClient.submitPayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                PAYMENT_TRANSACTION_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willReturn(new ResponseEntity<>(response, HttpStatus.OK));

        final SubmitPaymentResponse actualResponse = instance.submitPayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                PAYMENT_TRANSACTION_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        );

        assertThat(actualResponse).isEqualTo(response);
    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenSubmitPayment_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        final SubmitPaymentRequest request = Instancio.create(SubmitPaymentRequest.class);
        given(interacPaymentsApiClient.submitPayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                PAYMENT_TRANSACTION_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue));

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.submitPayment(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                RETRY_INDICATOR,
                PAYMENT_TRANSACTION_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        ));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenTransferCancelBegin_thenResponseReturned() {
        final PaymentsCancelTransactionPostRequestModel request = Instancio.create(PaymentsCancelTransactionPostRequestModel.class);
        final CancelPaymentTransaction response = Instancio.create(CancelPaymentTransaction.class);
        given(interacPaymentsApiClient.transferCancelBegin(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willReturn(new ResponseEntity<>(response, HttpStatus.OK));

        final CancelPaymentTransaction actualResponse = instance.transferCancelBegin(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        );

        assertThat(actualResponse).isEqualTo(response);
    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenTransferCancelBegin_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {
        final PaymentsCancelTransactionPostRequestModel request = Instancio.create(PaymentsCancelTransactionPostRequestModel.class);

        given(interacPaymentsApiClient.transferCancelBegin(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue));

        assertThatThrownBy(() -> instance.transferCancelBegin(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request
        )).isInstanceOf(
                expectedClass
        );
    }

    @Test
    void givenSuccessScenario_whentransferCancelRollbackBegin_thenResponseReturned() {
        given(interacPaymentsApiClient.transferCancelRollback(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                PARTICIPANT_CANCEL_TXN_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION)
        ).willReturn(new ResponseEntity<>(HttpStatus.OK));

        assertThatCode(() -> instance.transferCancelRollback(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                PARTICIPANT_CANCEL_TXN_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        )).doesNotThrowAnyException();

    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenTransferCancelRollbackBegin_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        given(interacPaymentsApiClient.transferCancelRollback(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                PARTICIPANT_CANCEL_TXN_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue));


        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.transferCancelRollback(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                END_TO_END_IDENTIFICATION,
                PARTICIPANT_CANCEL_TXN_TOKEN,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        ));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenTransferCancelCommitBegin_thenResponseReturned() {
        final PaymentsCancelTransactionPutRequestModel request = Instancio.create(PaymentsCancelTransactionPutRequestModel.class);
        given(interacPaymentsApiClient.transferCancelCommit(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                CANCEL_TRX_ID,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willReturn(new ResponseEntity<>(HttpStatus.OK));

        assertThatCode(() -> instance.transferCancelCommit(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                CANCEL_TRX_ID,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).doesNotThrowAnyException();

    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenTransferCancelCommitBegin_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        final PaymentsCancelTransactionPutRequestModel request = Instancio.create(PaymentsCancelTransactionPutRequestModel.class);
        given(interacPaymentsApiClient.transferCancelCommit(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                CANCEL_TRX_ID,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue)
        );

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.transferCancelCommit(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                CANCEL_TRX_ID,
                RETRY_INDICATOR,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION,
                request));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenReissueNotification_thenResponseReturned() {
        given(interacPaymentsApiClient.reissuePaymentNotification(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                INDIRECT_CONNECTOR_ID,
                null,
                new SendTransferNotice())
        ).willReturn(new ResponseEntity<>(HttpStatus.OK));

        assertThatCode(() -> instance.reissuePaymentNotification(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                INDIRECT_CONNECTOR_ID,
                null,
                new SendTransferNotice())
        ).doesNotThrowAnyException();

    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenReissueNotification_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        given(interacPaymentsApiClient.reissuePaymentNotification(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                INDIRECT_CONNECTOR_ID,
                null,
                new SendTransferNotice())
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue)
        );

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.reissuePaymentNotification(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ETRANSFER_ID,
                INDIRECT_CONNECTOR_ID,
                null,
                new SendTransferNotice()));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    @Test
    void givenSuccessScenario_whenGetPaymentOptions_thenResponseReturned() {
        final PaymentOptionResponse response = Instancio.create(PaymentOptionResponse.class);

        given(interacPaymentsApiClient.getPaymentOptions(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ProductCode.DOMESTIC,
                AccountAliasType.PHONE,
                PHONE_NUMBER,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION)
        ).willReturn(new ResponseEntity<>(response, HttpStatus.OK));

        final PaymentOptionResponse actualResponse = instance.getPaymentOptions(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ProductCode.DOMESTIC,
                AccountAliasType.PHONE,
                PHONE_NUMBER,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        );

        assertThat(actualResponse).isEqualTo(response);
    }

    @ParameterizedTest
    @MethodSource("InteracPaymentException")
    void givenException_whenGetPaymentOptions_thenThrowInteracException(
            final Exception thrownException,
            final Class<? extends OutgoingPaymentException> expectedClass,
            final Boolean isSocketTimeout) {

        given(interacPaymentsApiClient.getPaymentOptions(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ProductCode.DOMESTIC,
                AccountAliasType.PHONE,
                PHONE_NUMBER,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION)
        ).willThrow(thrownException);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(thrownException)).willReturn(isSocketTimeoutValue)
        );

        final OutgoingPaymentException thrown = (OutgoingPaymentException) catchThrowable(() -> instance.getPaymentOptions(
                PARTICIPANT_ID,
                PARTICIPANT_USER_ID,
                REQUEST_ID,
                CHANNEL_INDICATOR,
                API_SIGNATURE,
                API_SIGNATURE_TYPE,
                TRANSACTION_TIME,
                ProductCode.DOMESTIC,
                AccountAliasType.PHONE,
                PHONE_NUMBER,
                INDIRECT_CONNECTOR_ID,
                AUTHORISATION
        ));

        assertThat(thrown).isInstanceOf(expectedClass);
    }

    private static Stream<Arguments> InteracPaymentException() {
        return Stream.of(
                Arguments.of(
                        Instancio.create(InteracException.class),
                        InteracException.class,
                        null),
                Arguments.of(
                        Instancio.create(RetryableException.class),
                        InteracTimeoutException.class,
                        Boolean.TRUE),
                Arguments.of(
                        Instancio.create(RuntimeException.class),
                        InteracException.class,
                        Boolean.FALSE)
        );
    }

}
