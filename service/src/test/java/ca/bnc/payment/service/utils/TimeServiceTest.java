package ca.bnc.payment.service.utils;

import ca.bnc.payment.util.TimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Clock;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;

class TimeServiceTest {

    private static final String FORMATTED_LOCAL_DATE_TIME = "2024-01-01T00:00:00.000Z";
    private static final OffsetDateTime OFFSET_DATE_TIME = OffsetDateTime.parse("2024-01-01T00:00:00.000Z");
    private static final LocalDateTime LOCAL_DATE_TIME = LocalDateTime.parse("2024-01-01T00:00:00.000");
    private static final Clock clock = Clock.fixed(LOCAL_DATE_TIME.toInstant(UTC), UTC);

    private TimeService instance;

    @BeforeEach
    void setUp() {
        instance = new TimeService(clock);
    }

    @Test
    void whenGetNowOffetDateTime_willReturn() {
        final OffsetDateTime result = instance.getNowOffsetDateTime();
        assertThat(result).isEqualTo(OFFSET_DATE_TIME);
    }

    @Test
    void whenGetNowLocalDateTime_willReturn() {
        final LocalDateTime result = instance.getNowLocalDateTime();
        assertThat(result).isEqualTo(LOCAL_DATE_TIME);
    }

    @Test
    void whenFormatLocalDateTime_willReturn() {
        final String result = instance.formatLocalDateTime(LOCAL_DATE_TIME);
        assertThat(result).isEqualTo(FORMATTED_LOCAL_DATE_TIME);
    }

    @Test
    void whenGetFormattedNowLocalDateTime_willReturn() {
        final String result = instance.getFormattedNowLocalDateTime();
        assertThat(result).isEqualTo(FORMATTED_LOCAL_DATE_TIME);
    }
}
