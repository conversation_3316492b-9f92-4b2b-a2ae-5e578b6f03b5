package ca.bnc.payment.service.action;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.OptionPaymentType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentOption;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentOptionResponse;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

class InteracGetPaymentOptionsUpdaterTest {

    private static final Set<OptionPaymentType> AUTO_DEPOSIT_PAYMENT_TYPES =
            Set.of(OptionPaymentType.ACCOUNT_ALIAS_PAYMENT, OptionPaymentType.REALTIME_ACCOUNT_ALIAS_PAYMENT);

    private InteracGetPaymentOptionsUpdater testee;

    @BeforeEach
    void setUp() {
        testee = new InteracGetPaymentOptionsUpdater();
    }

    @ParameterizedTest(name = "OptionPaymentType = {0}")
    @MethodSource("depositPaymentTypesProvider")
    void testUpdateRequestDtoPaymentType_GivenAtLeastOneEligiblePaymentTypesDtoToRegularPaymentOptionst(final OptionPaymentType eligiblePaymentType) {
        final List<PaymentOption> paymentOptions = List.of(
                createPaymentOption(eligiblePaymentType),
                createPaymentOption(OptionPaymentType.REALTIME_ACCOUNT_DEPOSIT_PAYMENT),
                createPaymentOption(OptionPaymentType.REGULAR_PAYMENT)
        );


        final PaymentOptionResponse paymentOptionResponse = createPaymentOptionsResponse(paymentOptions);

        final RequestDto<SendPaymentRequest> requestDto = createRequestDtoWithPaymentType(LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);
        testee.updateRequestDtoToRegularPayment(paymentOptionResponse, requestDto);
        assertThat(requestDto.getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument().getProprietary()).isEqualTo(LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT);
    }


    @ParameterizedTest(name = "OptionPaymentType = {0}")
    @MethodSource("ineligiblePaymentTypesProvider")
    void testUpdateRequestDtoPaymentType_GivenNoEligiblePaymentTypesDtoToRegularPaymentOptionst(final OptionPaymentType ineligiblePaymentType) {
        final List<PaymentOption> paymentOptions = List.of(
                createPaymentOption(ineligiblePaymentType),
                createPaymentOption(OptionPaymentType.REGULAR_PAYMENT)
        );


        final PaymentOptionResponse paymentOptionResponse = createPaymentOptionsResponse(paymentOptions);

        final RequestDto<SendPaymentRequest> requestDto = createRequestDtoWithPaymentType(LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_ALIAS_PAYMENT);
        testee.updateRequestDtoToRegularPayment(paymentOptionResponse, requestDto);
        assertThat(requestDto.getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument().getProprietary()).isEqualTo(LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT);
    }


    private PaymentOptionResponse createPaymentOptionsResponse(List<PaymentOption> paymentOptions) {
        return Instancio.of(PaymentOptionResponse.class)
                .set(field(PaymentOptionResponse::getPaymentOptions), paymentOptions)
                .create();
    }

    private PaymentOption createPaymentOption(final OptionPaymentType paymentType) {
        return Instancio.of(PaymentOption.class)
                .set(field(PaymentOption::getPaymentType), paymentType)
                .create();
    }


    private static Stream<Arguments> depositPaymentTypesProvider() {
        return Stream.of(
                AUTO_DEPOSIT_PAYMENT_TYPES
                        .stream()
                        .map(Arguments::of).toArray(Arguments[]::new)
        );
    }

    private static Stream<Arguments> ineligiblePaymentTypesProvider() {
        final Set<OptionPaymentType> nonEligiblePaymentAccountTypes =
                Arrays.stream(OptionPaymentType.values())
                        .filter(paymentType -> !AUTO_DEPOSIT_PAYMENT_TYPES.contains(paymentType))
                        .collect(Collectors.toSet());

        return Stream.of(
                nonEligiblePaymentAccountTypes
                        .stream()
                        .map(Arguments::of).toArray(Arguments[]::new)
        );
    }

    private RequestDto<SendPaymentRequest> createRequestDtoWithPaymentType(LocalInstrument2Choice.ProprietaryEnum proprietaryEnum) {
        final RequestDto<SendPaymentRequest> requestDto =
                Instancio.of(new TypeToken<RequestDto<SendPaymentRequest>>() {
                }).create();

        requestDto
                .getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentTypeInformation()
                .getLocalInstrument()
                .setProprietary(proprietaryEnum);
        return requestDto;
    }

}