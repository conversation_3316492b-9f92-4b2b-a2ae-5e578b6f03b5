package ca.bnc.payment.service.interac;

import ca.bnc.payment.exception.validation.IdentifierNotFoundException;
import ca.bnc.payment.facade.PartyFacade;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.repository.OutgoingPaymentRepository;
import ca.bnc.payment.utils.RequestDtoTestUtils;
import ca.nbc.payment.pmtpartnersparty.model.Identifier;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static ca.bnc.payment.constant.PartyErrorMessage.INTERAC_ID_NOT_FOUND;
import static ca.bnc.payment.utils.RequestDtoTestUtils.X_CLIENT_ID;
import static ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus.ACTIVE;
import static ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus.INACTIVE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracIdentifierServiceTest {

    @Mock
    private PartyFacade partyFacade;
    
    @Mock
    private OutgoingPaymentRepository outgoingPaymentRepository;

    private InteracIdentifierService testee;

    @BeforeEach
    void setUp() {
        testee = new InteracIdentifierService(partyFacade, outgoingPaymentRepository);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyFacade, outgoingPaymentRepository);
    }

    @Test
    void shouldReturnActiveIdentifierWhenOneExists() {
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        final Identifier activeIdentifier = Instancio.of(Identifier.class)
                .set(field(Identifier::getStatus), ACTIVE)
                .create();
        final Identifiers identifiers = Instancio.of(Identifiers.class)
                .set(field(Identifiers::getIdentifiers), List.of(activeIdentifier))
                .create();
        
        given(partyFacade.getPartyClientById(requestDto)).willReturn(identifiers);

        final Identifier actual = testee.findActiveIdentifier(requestDto);

        assertThat(actual).isEqualTo(activeIdentifier);
    }

    @Test
    void shouldThrowExceptionAndDeletePaymentWhenNoActiveIdentifierExists() {
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        final Identifier activeIdentifier = Instancio.of(Identifier.class)
                .set(field(Identifier::getStatus), INACTIVE)
                .create();
        final Identifiers identifiers = Instancio.of(Identifiers.class)
                .set(field(Identifiers::getIdentifiers), List.of(activeIdentifier))
                .create();
        
        given(partyFacade.getPartyClientById(requestDto)).willReturn(identifiers);

        assertThatThrownBy(() -> testee.findActiveIdentifier(requestDto))
                .isInstanceOf(IdentifierNotFoundException.class)
                .hasMessage(INTERAC_ID_NOT_FOUND.formatted(X_CLIENT_ID));
                
        then(outgoingPaymentRepository).should().delete(requestDto.getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification());
    }

}