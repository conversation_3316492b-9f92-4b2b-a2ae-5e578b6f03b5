package ca.bnc.payment.mapper.pptd;


import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.Contact4;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.PartyIdentification135;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.PaymentAuthentication;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorIdentification;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PptdPaymentAuthenticationMapperTest {

    private PptdPaymentAuthenticationMapper testee;

    @BeforeEach
    void setup() {
        testee = new PptdPaymentAuthenticationMapper();
    }

    @Test
    void map() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentAuthentication outgoingPaymentAuthentication
                = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentAuthentication.class);

        PaymentAuthentication pptdPaymentAuthentication = testee.mapPaymentAuthentication(outgoingPaymentAuthentication);

        assertThat(pptdPaymentAuthentication.getSecurityQuestion()).isEqualTo(outgoingPaymentAuthentication.getSecurityQuestion());
        assertThat(pptdPaymentAuthentication.getSecurityAnswer()).isEqualTo(outgoingPaymentAuthentication.getSecurityAnswer());
    }

}
