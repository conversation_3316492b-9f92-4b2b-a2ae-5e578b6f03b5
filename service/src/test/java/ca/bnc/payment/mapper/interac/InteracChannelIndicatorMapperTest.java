package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ChannelIndicator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class InteracChannelIndicatorMapperTest {

    private InteracChannelIndicatorMapper testee;

    @BeforeEach
    void setUp() {
        testee = new InteracChannelIndicatorMapper();
    }

    @Test
    void testMap_Web() {
        assertThat(testee.map("WEB")).isEqualTo(ChannelIndicator.ONLINE);
    }

    @Test
    void testMap_Mobile() {
        assertThat(testee.map("MOBILE")).isEqualTo(ChannelIndicator.MOBILE);
    }

    @Test
    void testMap_IllegalArgumentException() {
        String source = "ILLEGAL";

        assertThatThrownBy(() -> testee.map(source))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Unexpected value: " + source);
    }

}
