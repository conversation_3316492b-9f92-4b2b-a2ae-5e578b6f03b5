package ca.bnc.payment.mapper.domesticcreditors;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.pmt_outgoing_payment_management_domestic_creditors_resources.generated.model.Error;
import ca.bnc.payment.pmt_outgoing_payment_management_domestic_creditors_resources.generated.model.Errors;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class DomesticCreditorsErrorsMapperTest {

    private DomesticCreditorsErrorsMapper domesticCreditorsErrorsMapper;

    @BeforeEach
    void setUp() {
        domesticCreditorsErrorsMapper = new DomesticCreditorsErrorsMapper();
    }

    @Test
    void givenDomesticCreditorsErrorModel_WhenMapperCalled_ThenReturnErrors() {
        final Error error = Instancio.create(Error.class);

        final Errors errorModel = new Errors();
        errorModel.setErrors(List.of(error));

        final OutgoingPaymentException.Error actualError =
                domesticCreditorsErrorsMapper.mapDomesticCreditorsErrorModelToOutgoingPaymentExceptionError(errorModel);

        assertThat(actualError.code()).isEqualTo(error.getCode());
        assertThat(actualError.text()).isEqualTo(error.getText());
    }

}
