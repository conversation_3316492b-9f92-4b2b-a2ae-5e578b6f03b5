package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.GroupHeader93;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PaymentNotificationGroupHeaderMapperTest {

    private PaymentNotificationGroupHeaderMapper testee;

    @BeforeEach
    void setUp() {
        testee = new PaymentNotificationGroupHeaderMapper();
    }

    @Test
    void mapGroupHeader() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GroupHeader93 groupHeader93
                = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GroupHeader93.class);

        GroupHeader93 result = testee.mapGroupHeader(groupHeader93);

        assertThat(result.getMessageIdentification()).isEqualTo(groupHeader93.getMessageIdentification());
        assertThat(result.getCreationDateTime()).isEqualTo(groupHeader93.getCreationDateTime());
        assertThat(result.getNumberOfTransactions()).isEqualTo(groupHeader93.getNumberOfTransactions());
    }


}