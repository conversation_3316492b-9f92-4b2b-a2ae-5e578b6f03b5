package ca.bnc.payment.mapper.pptd;


import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.AccountIdentification4Choice;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class PptdAccountIdentificationMapperTest {

    private PptdAccountIdentificationMapper testee;

    @Mock
    private PptdGenericAccountIdentificationMapper pptdGenericAccountIdentificationMapper;

    @BeforeEach
    void setup() {
        testee = new PptdAccountIdentificationMapper(pptdGenericAccountIdentificationMapper);
    }

    @Test
    void map() {
        AccountIdentification4Choice pptdAccountIdentification4Choice = Instancio.create(AccountIdentification4Choice.class);
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.AccountIdentification4Choice managementIdentification4Choice1 = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.AccountIdentification4Choice.class);
        given(pptdGenericAccountIdentificationMapper.mapGenericAccountIdentification(managementIdentification4Choice1.getOther())).willReturn(pptdAccountIdentification4Choice.getOther());

        AccountIdentification4Choice actual = testee.mapAccountIdentification(managementIdentification4Choice1);

        assertThat(actual.getOther()).isEqualTo(pptdAccountIdentification4Choice.getOther());
    }

}
