package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentTypeInformation28;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation27;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacPaymentTypeInformation27MapperTest {


    private BacPaymentTypeInformation27Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacPaymentTypeInformation27Mapper();
    }

    @Test
    void givenPaymentTypeInformation_whenMap_thenReturnBankAccountConnectorEquivalent() {
        PaymentTypeInformation28 paymentTypeInformation = Instancio.create(PaymentTypeInformation28.class);

        PaymentTypeInformation27 result = mapper.map(paymentTypeInformation);

        assertThat(result).isNotNull();
        assertThat(((LocalInstrument2ChoiceProprietary) result.getLocalInstrument()).getProprietary().getValue())
                .isEqualTo(paymentTypeInformation.getLocalInstrument().getProprietary().getValue());
    }
}
