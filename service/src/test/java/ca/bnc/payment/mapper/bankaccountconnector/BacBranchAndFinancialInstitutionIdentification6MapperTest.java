package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacBranchAndFinancialInstitutionIdentification6MapperTest {

    private BacBranchAndFinancialInstitutionIdentification6Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacBranchAndFinancialInstitutionIdentification6Mapper();
    }

    @Test
    void givenSystemMemberIdentification_whenMap_thenReturnBranchAndFinancialInstitutionIdentification() {
        String systemMemberIdentification = "systemMemberIdentification";

        BranchAndFinancialInstitutionIdentification6 result = mapper.map(systemMemberIdentification);

        assertThat(result).isNotNull();
        assertThat(result.getFinancialInstitutionIdentification().getClearingSystemMemberIdentification().getMemberIdentification())
                .isEqualTo(systemMemberIdentification);
    }
}
