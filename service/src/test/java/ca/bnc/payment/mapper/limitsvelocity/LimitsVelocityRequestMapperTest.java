package ca.bnc.payment.mapper.limitsvelocity;

import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.ClientType;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.LimitType;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesDecrement;
import ca.bnc.payment.pmt_outgoing_payment_management_limits_velocity_resources.generated.model.VelocitiesIncrement;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.ActiveCurrencyAndAmount;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.LocalInstrument2Choice;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentTypeInformation28;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import ca.bnc.payment.util.TimeService;
import ca.bnc.payment.util.IdGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class LimitsVelocityRequestMapperTest {

    public static final OffsetDateTime NOW = OffsetDateTime.now();
    private static final String IDENTIFICATION = "identification";
    private static final String E_2_E_IDENTIFICATION = "e2eIdentification";
    private static final String INSTRUCTION_IDENTIFICATION = "instructionIdentification";
    private static final BigDecimal AMOUNT = new BigDecimal(100);
    private static final String COMMAND_ID = "c48c372b265b4aeab0fe228a49db8ffb";

    private LimitsVelocityRequestMapper limitsVelocityRequestMapper;

    @Mock
    private SendPaymentContext sendPaymentContext;
    @Mock
    private TaskContext<SendPaymentRequest> taskContext;
    @Mock
    private TimeService timeService;
    @Mock
    private IdGenerator idGenerator;

    @BeforeEach
    void setUp() {
        limitsVelocityRequestMapper = new LimitsVelocityRequestMapper(timeService, idGenerator);
    }

    @ParameterizedTest
    @MethodSource("limitTypesMapping")
    void testMapIncrement(
            final LocalInstrument2Choice.ProprietaryEnum actualLimitType,
            final LimitType expectedLimitType,
            final SupplementaryData.ClientTypeEnum clientTypeEnum,
            final ClientType expectedClientType) {

        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        given(taskContext.getClientId()).willReturn(IDENTIFICATION);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);

        CreditTransferTransaction39 creditTransferTransactionInformation = new CreditTransferTransaction39();

        PaymentIdentification7 paymentIdentification = new PaymentIdentification7();
        paymentIdentification.setEndToEndIdentification(E_2_E_IDENTIFICATION);
        paymentIdentification.setInstructionIdentification(INSTRUCTION_IDENTIFICATION);
        creditTransferTransactionInformation.setPaymentIdentification(paymentIdentification);

        SupplementaryData supplementaryData = new SupplementaryData();
        supplementaryData.setClientType(clientTypeEnum);
        creditTransferTransactionInformation.setSupplementaryData(supplementaryData);

        ActiveCurrencyAndAmount activeCurrencyAndAmount = new ActiveCurrencyAndAmount();
        activeCurrencyAndAmount.setAmount(AMOUNT);
        creditTransferTransactionInformation.setInterbankSettlementAmount(activeCurrencyAndAmount);

        PaymentTypeInformation28 paymentTypeInformation = new PaymentTypeInformation28();
        LocalInstrument2Choice localInstrument = new LocalInstrument2Choice();
        localInstrument.setProprietary(actualLimitType);
        paymentTypeInformation.setLocalInstrument(localInstrument);
        creditTransferTransactionInformation.setPaymentTypeInformation(paymentTypeInformation);

        SendPaymentRequest sendPaymentRequest = new SendPaymentRequest();
        sendPaymentRequest.setFiToFICustomerCreditTransfer(new FIToFICustomerCreditTransferV08());
        sendPaymentRequest.getFiToFICustomerCreditTransfer().setCreditTransferTransactionInformation(creditTransferTransactionInformation);
        given(taskContext.getRequestBody()).willReturn(sendPaymentRequest);

        VelocitiesIncrement velocitiesIncrement = limitsVelocityRequestMapper.mapIncrement(sendPaymentContext);

        assertThat(velocitiesIncrement.getEndToEndIdentification()).isEqualTo(E_2_E_IDENTIFICATION);
        assertThat(velocitiesIncrement.getInstructionIdentification()).isEqualTo(INSTRUCTION_IDENTIFICATION);
        assertThat(velocitiesIncrement.getLimitType()).isEqualTo(expectedLimitType);
        assertThat(velocitiesIncrement.getClientType()).isEqualTo(expectedClientType);
        assertThat(velocitiesIncrement.getAmount()).isEqualTo(AMOUNT);
        assertThat(velocitiesIncrement.getPostingDate()).isEqualTo(NOW);
    }

    @Test
    void testMapDecrement() {

        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        given(idGenerator.generateCommandId()).willReturn(COMMAND_ID);

        CreditTransferTransaction39 creditTransferTransactionInformation = new CreditTransferTransaction39();

        PaymentIdentification7 paymentIdentification = new PaymentIdentification7();
        paymentIdentification.setEndToEndIdentification(E_2_E_IDENTIFICATION);
        paymentIdentification.setInstructionIdentification(INSTRUCTION_IDENTIFICATION);
        creditTransferTransactionInformation.setPaymentIdentification(paymentIdentification);

        SendPaymentRequest sendPaymentRequest = new SendPaymentRequest();
        sendPaymentRequest.setFiToFICustomerCreditTransfer(new FIToFICustomerCreditTransferV08());
        sendPaymentRequest.getFiToFICustomerCreditTransfer().setCreditTransferTransactionInformation(creditTransferTransactionInformation);
        given(taskContext.getRequestBody()).willReturn(sendPaymentRequest);

        VelocitiesDecrement velocitiesDecrement = limitsVelocityRequestMapper.mapDecrement(sendPaymentContext);

        assertThat(velocitiesDecrement.getEndToEndIdentification()).isEqualTo(E_2_E_IDENTIFICATION);
        assertThat(velocitiesDecrement.getInstructionIdentification()).isEqualTo(COMMAND_ID);
        assertThat(velocitiesDecrement.getOriginalInstructionIdentification()).isEqualTo(INSTRUCTION_IDENTIFICATION);

    }

    private static Stream<Arguments> limitTypesMapping() {
        return Stream.of(
                Arguments.of(
                        LocalInstrument2Choice.ProprietaryEnum.ACCOUNT_DEPOSIT_PAYMENT,
                        LimitType.DOMESTIC_INTERAC_ANR,
                        SupplementaryData.ClientTypeEnum.INDIVIDUAL,
                        ClientType.INDIVIDUAL),
                Arguments.of(
                        LocalInstrument2Choice.ProprietaryEnum.REALTIME_ACCOUNT_DEPOSIT_PAYMENT,
                        LimitType.DOMESTIC_INTERAC_RTANR,
                        SupplementaryData.ClientTypeEnum.INDIVIDUAL,
                        ClientType.INDIVIDUAL),
                Arguments.of(
                        LocalInstrument2Choice.ProprietaryEnum.REGULAR_PAYMENT,
                        LimitType.DOMESTIC_INTERAC_REGULAR,
                        SupplementaryData.ClientTypeEnum.ORGANIZATION,
                        ClientType.ORGANISATION),
                Arguments.of(
                        LocalInstrument2Choice.ProprietaryEnum.FULFILL_REQUEST_FOR_PAYMENT,
                        LimitType.DOMESTIC_INTERAC_MONEYREQUEST,
                        SupplementaryData.ClientTypeEnum.ORGANIZATION,
                        ClientType.ORGANISATION)
        );
    }

}
