package ca.bnc.payment.mapper.fraud;

import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.from;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class FraudHeaderMapperTest {

    private static final String TRACE_PARENT = "00-0af7651916cd43dd8448eb211c80319c-00f067aa0ba902b7-01";
    private static final String BUSINESS_ID = "00f067aa";
    private static final UUID BNC_CORRELATION_ID = UUID.fromString("e7fb0027-a498-4661-bb00-27a498e66109");

    @Mock
    private FraudApiIdGenerator fraudIdGenerator;

    @Mock
    private SendPaymentContext sendPaymentContext;

    @Mock
    private TaskContext<SendPaymentRequest> taskContext;

    private FraudHeaderMapper fraudHeaderMapper;

    @BeforeEach
    void setUp() {
        fraudHeaderMapper = new FraudHeaderMapper(fraudIdGenerator);
    }

    @Test
    void testMap() {
        given(fraudIdGenerator.generateTraceParentId()).willReturn(TRACE_PARENT);
        given(fraudIdGenerator.generateBncCorrelationId()).willReturn(BNC_CORRELATION_ID);
        given(sendPaymentContext.getTaskContext()).willReturn(taskContext);
        given(taskContext.getEndToEndIdentification()).willReturn(BUSINESS_ID);
        final FraudHeaderMapper.FraudHeader fraudHeader = fraudHeaderMapper.map(sendPaymentContext);
        final FraudHeaderMapper.FraudHeader expectedFraudHeader = FraudHeaderMapper.FraudHeader.fromRequiredFields(
                BUSINESS_ID, TRACE_PARENT, BNC_CORRELATION_ID
        );

        assertThat(fraudHeader).isEqualTo(expectedFraudHeader);
    }

    @Test
    void testHeaderInit() {
        FraudHeaderMapper.FraudHeader header = FraudHeaderMapper.FraudHeader.fromRequiredFields(
                BUSINESS_ID, TRACE_PARENT, BNC_CORRELATION_ID
        );
        assertThat(header)
                .isNotNull()
                .returns(BUSINESS_ID, from(FraudHeaderMapper.FraudHeader::bncEndToEndBusinessID))
                .returns(TRACE_PARENT, from(FraudHeaderMapper.FraudHeader::traceParent))
                .returns(BNC_CORRELATION_ID, from(FraudHeaderMapper.FraudHeader::bncCorrelationID))
                .returns(null, from(FraudHeaderMapper.FraudHeader::traceState));
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(fraudIdGenerator, sendPaymentContext, taskContext);
    }
}