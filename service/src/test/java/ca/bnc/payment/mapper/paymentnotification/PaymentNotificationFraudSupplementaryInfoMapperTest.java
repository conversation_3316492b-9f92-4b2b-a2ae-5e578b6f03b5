package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.SupplementaryInfo;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PaymentNotificationFraudSupplementaryInfoMapperTest {

    private PaymentNotificationFraudSupplementaryInfoMapper testee;

    @BeforeEach
    void setUp() {
        testee = new PaymentNotificationFraudSupplementaryInfoMapper();
    }

    @Test
    void mapFraudSupplementaryInfo() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryInfo supplementaryInfo
                = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryInfo.class);

        SupplementaryInfo result = testee.mapFraudSupplementaryInfo(supplementaryInfo);

        assertThat(result.getAccountCreationDate()).isEqualTo(supplementaryInfo.getAccountCreationDate());
        assertThat(result.getClientCardNumber()).isEqualTo(supplementaryInfo.getClientCardNumber());
        assertThat(result.getClientDeviceFingerPrint()).isEqualTo(supplementaryInfo.getClientDeviceFingerPrint());
        assertThat(result.getClientIpAddress()).isEqualTo(supplementaryInfo.getClientIpAddress());
        assertThat(result.getClientAuthenticationMethod().getValue()).isEqualTo(supplementaryInfo.getClientAuthenticationMethod().getValue());
    }

}