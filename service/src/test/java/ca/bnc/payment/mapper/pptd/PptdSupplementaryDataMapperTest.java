package ca.bnc.payment.mapper.pptd;


import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.FraudCheckResult;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.FraudSupplementaryInfo;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.MandateRelatedInformation;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.PaymentAuthentication;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.SupplementaryData;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.ExternalPaymentTransactionStatus1Code.ACSP;
import static ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.ExternalPaymentTransactionStatus1Code.RJCT;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class PptdSupplementaryDataMapperTest {

    private PptdSupplementaryDataMapper testee;

    @Mock
    private PptdFraudSupplementaryInfoMapper pptdFraudSupplementaryInfoMapper;
    @Mock
    private PptdPaymentAuthenticationMapper pptdPaymentAuthenticationMapper;
    @Mock
    private PptdMandateRelatedInformationMapper pptdMandateRelatedInformationMapper;

    @BeforeEach
    void setup() {
        testee = new PptdSupplementaryDataMapper(pptdFraudSupplementaryInfoMapper, pptdPaymentAuthenticationMapper, pptdMandateRelatedInformationMapper);
    }

    @Test
    void mapAccepted() {
        CreditTransferTransaction39 outgoingCreditTransferTransaction = Instancio.create(CreditTransferTransaction39.class);
        String clearingSystemReference = "clearingSystemReference";
        String fraudDecision = "ALLOW";
        String debtorInteracId = "debtorInteracId";
        FraudSupplementaryInfo fraudSupplementaryInfo = Instancio.create(FraudSupplementaryInfo.class);
        given(pptdFraudSupplementaryInfoMapper.mapFraudSupplementaryInfo(outgoingCreditTransferTransaction.getSupplementaryData().getFraudSupplementaryInfo())).willReturn(fraudSupplementaryInfo);
        PaymentAuthentication paymentAuthentication = Instancio.create(PaymentAuthentication.class);
        given(pptdPaymentAuthenticationMapper.mapPaymentAuthentication(outgoingCreditTransferTransaction.getSupplementaryData().getPaymentAuthentication())).willReturn(paymentAuthentication);
        MandateRelatedInformation mandateRelatedInformation = Instancio.create(MandateRelatedInformation.class);
        given(pptdMandateRelatedInformationMapper.mapMandateRelatedInformation(outgoingCreditTransferTransaction.getMandateRelatedInformation())).willReturn(mandateRelatedInformation);

        SupplementaryData supplementaryData = testee.mapSupplementaryData(outgoingCreditTransferTransaction, clearingSystemReference, fraudDecision, debtorInteracId, null, null);

        assertThat(supplementaryData.getPaymentDirection()).isEqualTo(SupplementaryData.PaymentDirectionEnum.OUT);
        assertThat(supplementaryData.getTransactionStatus()).isEqualTo(ACSP);
        assertThat(supplementaryData.getFraudCheckResult().getAction()).isEqualTo(FraudCheckResult.ActionEnum.ALLOW);
    }

    @Test
    void mapRejected() {
        CreditTransferTransaction39 outgoingCreditTransferTransaction = Instancio.create(CreditTransferTransaction39.class);
        String clearingSystemReference = "clearingSystemReference";
        String fraudDecision = "ALLOW";
        String debtorInteracId = "debtorInteracId";
        FraudSupplementaryInfo fraudSupplementaryInfo = Instancio.create(FraudSupplementaryInfo.class);
        given(pptdFraudSupplementaryInfoMapper.mapFraudSupplementaryInfo(outgoingCreditTransferTransaction.getSupplementaryData().getFraudSupplementaryInfo())).willReturn(fraudSupplementaryInfo);
        PaymentAuthentication paymentAuthentication = Instancio.create(PaymentAuthentication.class);
        given(pptdPaymentAuthenticationMapper.mapPaymentAuthentication(outgoingCreditTransferTransaction.getSupplementaryData().getPaymentAuthentication())).willReturn(paymentAuthentication);
        MandateRelatedInformation mandateRelatedInformation = Instancio.create(MandateRelatedInformation.class);
        given(pptdMandateRelatedInformationMapper.mapMandateRelatedInformation(outgoingCreditTransferTransaction.getMandateRelatedInformation())).willReturn(mandateRelatedInformation);

        SupplementaryData supplementaryData = testee.mapSupplementaryData(outgoingCreditTransferTransaction, clearingSystemReference, fraudDecision, debtorInteracId, "errorCode", "errorText");

        assertThat(supplementaryData.getPaymentDirection()).isEqualTo(SupplementaryData.PaymentDirectionEnum.OUT);
        assertThat(supplementaryData.getTransactionStatus()).isEqualTo(RJCT);
        assertThat(supplementaryData.getStatusReasonInformation().getCode()).isEqualTo("errorCode");
        assertThat(supplementaryData.getStatusReasonInformation().getProprietary()).isEqualTo("errorText");
    }

}
