package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyAndAmount;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveCurrencyCode;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CashAccount38;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ChargeBearerType1Code;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemMemberIdentification2;
import ca.nbc.payment.etransfer.bankaccountconnector.model.CreditTransferTransaction39;
import ca.nbc.payment.etransfer.bankaccountconnector.model.FinancialInstitutionIdentification18;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GenericOrganisationIdentification1;
import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.OrganisationIdentification29;
import ca.nbc.payment.etransfer.bankaccountconnector.model.Party38ChoiceOrganizationIdentification;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PartyIdentification135;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentIdentification7;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation28;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static ca.bnc.payment.constant.PaymentManagementErrorCodes.NOT_PROVIDED;
import static ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary.ProprietaryEnum.REGULAR_PAYMENT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacCreditTransferTransaction39MapperTest {

    private BacCreditTransferTransaction39Mapper bacCreditTransferTransaction39Mapper;

    @Mock
    private BacPaymentIdentification7Mapper bacPaymentIdentification7Mapper;
    @Mock
    private BacActiveCurrencyAndAmountMapper bacActiveCurrencyAndAmountMapper;
    @Mock
    private BacPaymentTypeInformation28Mapper bacPaymentTypeInformation28Mapper;
    @Mock
    private BacCashAccount38Mapper bacCashAccount38Mapper;
    @Mock
    private BacPartyIdentification135Mapper bacPartyIdentification135Mapper;
    @Mock
    private BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;

    @BeforeEach
    void setUp() {
        bacCreditTransferTransaction39Mapper = new BacCreditTransferTransaction39Mapper(bacPaymentIdentification7Mapper,
                bacActiveCurrencyAndAmountMapper,
                bacPaymentTypeInformation28Mapper,
                bacCashAccount38Mapper,
                bacPartyIdentification135Mapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(bacPaymentIdentification7Mapper,
                bacActiveCurrencyAndAmountMapper,
                bacPaymentTypeInformation28Mapper,
                bacCashAccount38Mapper,
                bacPartyIdentification135Mapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @Test
    void givenDomesticCancellationPaymentRequestAndBankAccountConnectorDto_whenMap_thenReturnBankAccountConnectorEquivalent() {
        BigDecimal amount = new BigDecimal("1");
        String currency = "CAD";
        DomesticCancellationPaymentRequest domesticCancellationPaymentRequest = Instancio.create(DomesticCancellationPaymentRequest.class);
        PaymentIdentification7 paymentIdentification = Instancio.create(PaymentIdentification7.class);
        CashAccount38 creditorAccount = Instancio.create(CashAccount38.class);
        ActiveCurrencyAndAmount interbankSettlementAmount = new ActiveCurrencyAndAmount()
                .amount(amount)
                .currency(ActiveCurrencyCode.fromValue(currency));

        PaymentTypeInformation28 paymentTypeInformation28 = Instancio.create(PaymentTypeInformation28.class);
        LocalInstrument2ChoiceProprietary localInstrument2ChoiceOneOf = Instancio.create(LocalInstrument2ChoiceProprietary.class);
        localInstrument2ChoiceOneOf.setProprietary(REGULAR_PAYMENT);
        paymentTypeInformation28.setLocalInstrument(localInstrument2ChoiceOneOf);

        List<GenericOrganisationIdentification1> others = new ArrayList<>();
        others.add(new GenericOrganisationIdentification1().identification(NOT_PROVIDED));

        final PartyIdentification135 partyIdentification = new PartyIdentification135()
                .identification(new Party38ChoiceOrganizationIdentification()
                        .organisationIdentification(new OrganisationIdentification29()
                                .other(others)));
        final BranchAndFinancialInstitutionIdentification6 branchAndFinancialInstitutionIdentification6 =
                new BranchAndFinancialInstitutionIdentification6()
                        .financialInstitutionIdentification(new FinancialInstitutionIdentification18()
                                .clearingSystemMemberIdentification(new ClearingSystemMemberIdentification2()
                                        .memberIdentification(NOT_PROVIDED)));

        given(bacPaymentIdentification7Mapper.map(domesticCancellationPaymentRequest.getCreditTransferTransactionInformation().getPaymentIdentification()))
                .willReturn(paymentIdentification);
        given(bacPaymentTypeInformation28Mapper.map(REGULAR_PAYMENT.getValue()))
                .willReturn(paymentTypeInformation28);
        given(bacCashAccount38Mapper.map(domesticCancellationPaymentRequest.getCreditTransferTransactionInformation().getCreditorAccount()))
                .willReturn(creditorAccount);
        given(bacActiveCurrencyAndAmountMapper.map(currency, amount))
                .willReturn(interbankSettlementAmount);
        given(bacPartyIdentification135Mapper.mapDefaultPartyIdentification())
                .willReturn(partyIdentification);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(NOT_PROVIDED))
                .willReturn(branchAndFinancialInstitutionIdentification6);
        CreditTransferTransaction39 result = bacCreditTransferTransaction39Mapper.map(domesticCancellationPaymentRequest, amount, currency);

        assertThat(result).isNotNull();
        assertThat(result.getPaymentIdentification()).isEqualTo(paymentIdentification);
        assertThat(result.getChargeBearer()).isEqualTo(ChargeBearerType1Code.SLEV);
        assertThat(((LocalInstrument2ChoiceProprietary) result.getPaymentTypeInformation().getLocalInstrument()).getProprietary()).isEqualTo(REGULAR_PAYMENT);
        assertThat(result.getCreditorAccount()).isEqualTo(creditorAccount);
        assertThat(result.getInterbankSettlementAmount()).isEqualTo(interbankSettlementAmount);
        assertThat(result.getInterbankSettlementDate()).isEqualTo(domesticCancellationPaymentRequest.getGroupHeader().getCreationDateTime().toLocalDate());
        assertThat(result.getCreditor()).isEqualTo(partyIdentification);
        assertThat(result.getDebtor()).isEqualTo(partyIdentification);
        assertThat(result.getDebtorAgent()).isEqualTo(branchAndFinancialInstitutionIdentification6);
        assertThat(result.getCreditorAgent()).isEqualTo(branchAndFinancialInstitutionIdentification6);

    }

    @Test
    void givenDomesticCancellationPaymentRequestWithCreationDateNullAndBankAccountConnectorDto_whenMap_thenReturnBankAccountConnectorEquivalent() {
        BigDecimal amount = new BigDecimal("1");
        String currency = "CAD";
        DomesticCancellationPaymentRequest domesticCancellationPaymentRequest = Instancio.create(DomesticCancellationPaymentRequest.class);
        domesticCancellationPaymentRequest.getGroupHeader().setCreationDateTime(null);

        given(bacPaymentIdentification7Mapper.map(domesticCancellationPaymentRequest.getCreditTransferTransactionInformation().getPaymentIdentification()))
                .willReturn(null);
        given(bacPaymentTypeInformation28Mapper.map(REGULAR_PAYMENT.getValue()))
                .willReturn(null);
        given(bacCashAccount38Mapper.map(domesticCancellationPaymentRequest.getCreditTransferTransactionInformation().getCreditorAccount()))
                .willReturn(null);
        given(bacActiveCurrencyAndAmountMapper.map(currency, amount))
                .willReturn(null);
        given(bacPartyIdentification135Mapper.mapDefaultPartyIdentification())
                .willReturn(null);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(NOT_PROVIDED))
                .willReturn(null);

        CreditTransferTransaction39 result = bacCreditTransferTransaction39Mapper.map(domesticCancellationPaymentRequest, amount, currency);

        assertThat(result.getInterbankSettlementDate()).isNull();
    }
}
