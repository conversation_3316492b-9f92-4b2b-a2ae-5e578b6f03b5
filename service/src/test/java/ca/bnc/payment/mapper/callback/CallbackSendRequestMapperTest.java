package ca.bnc.payment.mapper.callback;

import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.CallbackSendRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.Error;
import ca.bnc.payment.pmt_outgoing_payment_management_callback_resources.generated.model.Errors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class CallbackSendRequestMapperTest {

    private CallbackSendRequestMapper callbackSendRequestMapper;

    @BeforeEach
    void setUp() {
        callbackSendRequestMapper = new CallbackSendRequestMapper();
    }

    @Test
    void givenErrorList_whenMap_thenReturnErrorCallBackSendRequest() {
        final Errors errors = new Errors();
        errors.addErrorsItem(new Error("code", "text", "origin"));

        final CallbackSendRequest request = callbackSendRequestMapper.map(errors);

        assertThat(request).isNotNull();
        assertThat(request.getErrors()).isEqualTo(errors.getErrors());
        assertThat(request.getStatus()).isEqualTo(CallbackSendRequest.StatusEnum.ERROR);
    }

    @Test
    void givenNullErrorList_whenMap_thenReturnSuccessCallBackSendRequest() {

        final CallbackSendRequest request = callbackSendRequestMapper.map(null);

        assertThat(request).isNotNull();
        assertThat(request.getErrors()).isNull();
        assertThat(request.getStatus()).isEqualTo(CallbackSendRequest.StatusEnum.SUCCESS);
    }
}