package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentIdentification7;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class InteracPaymentIdentification7MapperTest {

    private InteracPaymentIdentification7Mapper testee;

    @BeforeEach
    void setUp() {
        testee = new InteracPaymentIdentification7Mapper();
    }

    @Test
    void givenPaymentIdentification_whenMap_thenReturnInteracEquivalent() {

        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7 source =
                Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7.class);

        PaymentIdentification7 result = testee.map(source);

        assertThat(result.getInstructionIdentification()).isEqualTo(source.getInstructionIdentification());
        assertThat(result.getEndToEndIdentification()).isEqualTo(source.getEndToEndIdentification());
        assertThat(result.getTransactionIdentification()).isEqualTo(source.getEndToEndIdentification());
        assertThat(result.getClearingSystemReference()).isNull();

    }

}
