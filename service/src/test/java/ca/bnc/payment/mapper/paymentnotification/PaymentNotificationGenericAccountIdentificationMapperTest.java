package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.GenericAccountIdentification1;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class PaymentNotificationGenericAccountIdentificationMapperTest {

    private PaymentNotificationGenericAccountIdentificationMapper testee;

    @BeforeEach
    void setup() {
        testee = new PaymentNotificationGenericAccountIdentificationMapper();
    }

    @Test
    void mapGenericAccountIdentification() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GenericAccountIdentification1 genericAccountIdentification
                = new ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GenericAccountIdentification1();
        genericAccountIdentification.setIdentification("identification");

        GenericAccountIdentification1 result = testee.mapGenericAccountIdentification(genericAccountIdentification);

        assertEquals("identification", result.getIdentification());
    }

}