package ca.bnc.payment.mapper.bankaccountconnector;

import ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTypeInformation28;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static ca.nbc.payment.etransfer.bankaccountconnector.model.LocalInstrument2ChoiceProprietary.ProprietaryEnum.REGULAR_PAYMENT;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BacPaymentTypeInformation28MapperTest {

    private BacPaymentTypeInformation28Mapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacPaymentTypeInformation28Mapper();
    }

    @Test
    void givenProprietary_whenMap_thenReturnBankAccountConnectorEquivalent() {
        PaymentTypeInformation28 result = mapper.map(REGULAR_PAYMENT.getValue());

        assertThat(result).isNotNull();
        assertThat(((LocalInstrument2ChoiceProprietary) result.getLocalInstrument()).getProprietary())
                .isEqualTo(REGULAR_PAYMENT);
    }
}
