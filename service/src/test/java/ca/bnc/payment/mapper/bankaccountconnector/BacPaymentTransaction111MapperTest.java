package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ActiveOrHistoricCurrencyAndAmount;
import ca.nbc.payment.etransfer.bankaccountconnector.model.OriginalTransactionReference28;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentReversalReason9;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTransaction111;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacPaymentTransaction111MapperTest {
    private BacPaymentTransaction111Mapper bacPaymentTransaction111Mapper;

    @Mock
    private BacActiveOrHistoricCurrencyAndAmountMapper bacActiveOrHistoricCurrencyAndAmountMapper;
    @Mock
    private BacPaymentReversalReason9Mapper bacPaymentReversalReason9Mapper;
    @Mock
    private BacOriginalTransactionReference28Mapper bacOriginalTransactionReference28Mapper;

    @BeforeEach
    void setup() {
        bacPaymentTransaction111Mapper = new BacPaymentTransaction111Mapper(
                bacActiveOrHistoricCurrencyAndAmountMapper,
                bacPaymentReversalReason9Mapper,
                bacOriginalTransactionReference28Mapper);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                bacActiveOrHistoricCurrencyAndAmountMapper,
                bacPaymentReversalReason9Mapper,
                bacOriginalTransactionReference28Mapper);
    }

    @Test
    void map() {
        ActiveOrHistoricCurrencyAndAmount activeOrHistoricCurrencyAndAmount = Instancio.create(ActiveOrHistoricCurrencyAndAmount.class);
        PaymentReversalReason9 paymentReversalReason9 = Instancio.create(PaymentReversalReason9.class);
        OriginalTransactionReference28 originalTransactionReference28 = Instancio.create(OriginalTransactionReference28.class);
        CreditTransferTransaction39 creditTransferTransaction39 = Instancio.create(CreditTransferTransaction39.class);
        given(bacActiveOrHistoricCurrencyAndAmountMapper.map(creditTransferTransaction39)).willReturn(activeOrHistoricCurrencyAndAmount);
        given(bacPaymentReversalReason9Mapper.map()).willReturn(paymentReversalReason9);
        given(bacOriginalTransactionReference28Mapper.map(creditTransferTransaction39.getDebtorAccount().getIdentification().getOther().getIdentification())).willReturn(originalTransactionReference28);

        PaymentTransaction111 result = bacPaymentTransaction111Mapper.map(creditTransferTransaction39);

        assertThat(result.getOriginalTransactionIdentification()).isEqualTo(creditTransferTransaction39.getPaymentIdentification().getInstructionIdentification());
        assertThat(result.getReversedInterbankSettlementAmount()).isEqualTo(activeOrHistoricCurrencyAndAmount);
        assertThat(result.getReversalReasonInformation()).isEqualTo(List.of(paymentReversalReason9));
        assertThat(result.getOriginalTransactionReference()).isEqualTo(originalTransactionReference28);
    }
}
