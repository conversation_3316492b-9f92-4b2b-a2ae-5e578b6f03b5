package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditTransferTransaction39;
import ca.nbc.payment.etransfer.bankaccountconnector.model.PaymentTransaction111;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacPaymentTransaction111ListMapperTest {

    @Mock
    private BacPaymentTransaction111Mapper bacPaymentTransaction111Mapper;

    private BacPaymentTransaction111ListMapper bacPaymentTransaction111ListMapper;

    @BeforeEach
    void setup() {
        bacPaymentTransaction111ListMapper = new BacPaymentTransaction111ListMapper(bacPaymentTransaction111Mapper);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(bacPaymentTransaction111Mapper);
    }

    @Test
    void map() {
        PaymentTransaction111 paymentTransaction111 = Instancio.create(PaymentTransaction111.class);
        CreditTransferTransaction39 creditTransferTransaction39 = Instancio.create(CreditTransferTransaction39.class);
        given(bacPaymentTransaction111Mapper.map(creditTransferTransaction39)).willReturn(paymentTransaction111);

        List<PaymentTransaction111> result = bacPaymentTransaction111ListMapper.map(creditTransferTransaction39);

        assertThat(result).isEqualTo(List.of(paymentTransaction111));
    }
}
