package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.interac.RetryableInteracException;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ErrorModel;
import ca.bnc.payment.normalization.lib.service.NormalizationService;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.Errors;
import ca.bnc.payment.util.ErrorUtil;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static ca.bnc.payment.constant.PaymentManagementErrorCodes.TECHNICAL_ERROR;
import static ca.bnc.payment.constant.PaymentManagementRules.EMPTY_RULE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracErrorsMapperTest {

    private static final String CODE = "code";
    private static final String TEXT = "text";
    private static final String EXCEPTION_MESSAGE = "exception message";
    private static final String INTERAC_NORMALIZED_ERR_CODE = "InteracErrCodeToNormalizedErrCode";
    private static final String ORIGIN_SELF = "pmt-outgoing-domestic-payment-management";
    private static final String EXPECTED_TEXT = "exception message: text";

    @Mock
    private NormalizationService normalizationService;
    @Mock
    private ErrorUtil errorUtil;

    private InteracErrorsMapper testee;

    @BeforeEach
    void setUp() {
        testee = new InteracErrorsMapper(normalizationService, errorUtil);
    }

    @AfterEach
    void verifyNoUndesiredInteractions() {
        verifyNoMoreInteractions(normalizationService, errorUtil);
    }

    @Test
    void givenInteracErrorModel_WhenMapperCalled_ThenReturnErrors() {

        final ErrorModel source = Instancio.create(ErrorModel.class);

        final OutgoingPaymentException.Error result = testee.mapInteracErrorModelToError(source);

        assertThat(result.code()).isEqualTo(source.getCode());
        assertThat(result.text()).isEqualTo(source.getText());

    }

    @Test
    void givenExceptionWithErrors_WhenMapperCalled_ThenErrorMapped() {

        final OutgoingPaymentException source = new OutgoingPaymentException(
                EXCEPTION_MESSAGE,
                List.of(new OutgoingPaymentException.Error(CODE, TEXT)));
        given(normalizationService.normalize(INTERAC_NORMALIZED_ERR_CODE, CODE)).willReturn(Optional.empty());
        given(errorUtil.isTechnicalErrorCode(CODE)).willReturn(Boolean.FALSE);

        final Errors result = testee.map(source);

        assertThat(result.getErrors())
                .singleElement()
                .satisfies(error -> {
                    assertThat(error.getCode()).isEqualTo(TECHNICAL_ERROR);
                    assertThat(error.getText()).isEqualTo(EXPECTED_TEXT);
                    assertThat(error.getOrigin()).isEqualTo(ORIGIN_SELF);
                    assertThat(error.getRule()).isEqualTo(EMPTY_RULE);
                });

    }

    @Test
    void givenExceptionWithErrors_WhenMapperCalled_ThenTechnicalErrorMapped() {

        final OutgoingPaymentException source = new OutgoingPaymentException(EXCEPTION_MESSAGE);

        final Errors result = testee.map(source);

        assertThat(result.getErrors())
                .singleElement()
                .satisfies(error -> {
                    assertThat(error.getCode()).isEqualTo(TECHNICAL_ERROR);
                    assertThat(error.getText()).isEqualTo(source.getMessage());
                    assertThat(error.getOrigin()).isEqualTo(ORIGIN_SELF);
                    assertThat(error.getRule()).isEqualTo(EMPTY_RULE);
                });

    }

    @Test
    void givenExceptionWithCommonInteracResponseCodeErrors_WhenMapperCalled_ThenErrorMapped() {
        final OutgoingPaymentException source = new OutgoingPaymentException(
                EXCEPTION_MESSAGE,
                List.of(new OutgoingPaymentException.Error("999", TEXT)));
        given(normalizationService.normalize(INTERAC_NORMALIZED_ERR_CODE, "999")).willReturn(Optional.empty());
        given(errorUtil.isTechnicalErrorCode("999")).willReturn(Boolean.TRUE);

        final Errors result = testee.map(source);

        assertThat(result.getErrors())
                .singleElement()
                .satisfies(error -> {
                    assertThat(error.getCode()).isEqualTo(TECHNICAL_ERROR);
                    assertThat(error.getText()).isEqualTo(EXCEPTION_MESSAGE);
                    assertThat(error.getOrigin()).isEqualTo(ORIGIN_SELF);
                    assertThat(error.getRule()).isEqualTo(EMPTY_RULE);
                });

    }

    @Test
    void givenRetryableExceptionWithCommonInteracResponseCodeErrors_WhenMapperCalled_ThenErrorMapped() {
        final RetryableInteracException source = new RetryableInteracException(
                EXCEPTION_MESSAGE,
                new OutgoingPaymentException.Error("1", TEXT));
        given(normalizationService.normalize(INTERAC_NORMALIZED_ERR_CODE, "1")).willReturn(Optional.empty());
        given(errorUtil.isTechnicalErrorCode("1")).willReturn(Boolean.TRUE);

        final Errors result = testee.map(source);

        assertThat(result.getErrors())
                .singleElement()
                .satisfies(error -> {
                    assertThat(error.getCode()).isEqualTo(TECHNICAL_ERROR);
                    assertThat(error.getText()).isEqualTo(EXPECTED_TEXT);
                    assertThat(error.getOrigin()).isEqualTo(ORIGIN_SELF);
                    assertThat(error.getRule()).isEqualTo(EMPTY_RULE);
                });

    }

}
