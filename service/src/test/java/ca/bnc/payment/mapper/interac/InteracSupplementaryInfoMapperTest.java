package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SupplementaryInfo;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class InteracSupplementaryInfoMapperTest {

    private InteracSupplementaryInfoMapper testee;

    @BeforeEach
    void setUp() {
        testee = new InteracSupplementaryInfoMapper();
    }

    @Test
    void givenRemittanceInformation_whenMap_thenReturnInteracEquivalent() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData supplementaryData =
                Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData.class);

        SupplementaryInfo expected = new SupplementaryInfo()
                .customerIpAddress(supplementaryData.getFraudSupplementaryInfo().getClientIpAddress())
                .customerCardNumber(supplementaryData.getFraudSupplementaryInfo().getClientCardNumber())
                .accountCreationDate(supplementaryData.getFraudSupplementaryInfo().getAccountCreationDate())
                .customerAuthenticationMethod(SupplementaryInfo.CustomerAuthenticationMethodEnum.fromValue(
                        supplementaryData.getFraudSupplementaryInfo().getClientAuthenticationMethod().getValue()))
                .customerDeviceFingerPrint(supplementaryData.getFraudSupplementaryInfo().getClientDeviceFingerPrint());

        SupplementaryInfo result = testee.map(supplementaryData);

        assertThat(result).isEqualTo(expected);
    }

}
