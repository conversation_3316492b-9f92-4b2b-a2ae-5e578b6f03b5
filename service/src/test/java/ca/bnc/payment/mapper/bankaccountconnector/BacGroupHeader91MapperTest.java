package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader91;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacGroupHeader91MapperTest {

    private static final String INSTRUCTING_AGENT_MEMBER_IDENTIFICATION = "CA000612";
    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";

    private BacGroupHeader91Mapper mapper;

    @Mock
    private BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;

    @BeforeEach
    void setUp() {
        mapper = new BacGroupHeader91Mapper(bacBranchAndFinancialInstitutionIdentification6Mapper, INSTRUCTING_AGENT_MEMBER_IDENTIFICATION);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @Test
    void whenMapDomesticCancellationPaymentRequest_thenBranchAndFinancialInstitutionIdentification() {

        DomesticCancellationPaymentRequest domesticCancellationPaymentRequest = Instancio.create(DomesticCancellationPaymentRequest.class);
        BranchAndFinancialInstitutionIdentification6 instructingAgent =
                Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        BranchAndFinancialInstitutionIdentification6 instructedAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTING_AGENT_MEMBER_IDENTIFICATION))
                .willReturn(instructingAgent);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION))
                .willReturn(instructedAgent);

        GroupHeader91 result = mapper.map(domesticCancellationPaymentRequest);

        assertThat(result)
                .isNotNull()
                .satisfies(groupHeader91 -> {
                    assertThat(groupHeader91.getMessageIdentification())
                            .isEqualTo(domesticCancellationPaymentRequest.getGroupHeader().getMessageIdentification());
                    assertThat(groupHeader91.getCreationDatetime())
                            .isEqualTo(domesticCancellationPaymentRequest.getGroupHeader().getCreationDateTime());
                    assertThat(groupHeader91.getInstructingAgent()).isEqualTo(instructingAgent);
                    assertThat(groupHeader91.getInstructedAgent()).isEqualTo(instructedAgent);});

    }

    @Test
    void whenMapFiToFiCustomerCreditTransfer_thenBranchAndFinancialInstitutionIdentification() {

        FIToFICustomerCreditTransferV08 fiToFICustomerCreditTransfer = Instancio.create(FIToFICustomerCreditTransferV08.class);
        BranchAndFinancialInstitutionIdentification6 instructingAgent =
                Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        BranchAndFinancialInstitutionIdentification6 instructedAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTING_AGENT_MEMBER_IDENTIFICATION))
                .willReturn(instructingAgent);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION))
                .willReturn(instructedAgent);

        GroupHeader91 result = mapper.map(fiToFICustomerCreditTransfer);

        assertThat(result)
                .isNotNull()
                .satisfies(groupHeader91 -> {
                    assertThat(groupHeader91.getMessageIdentification())
                            .isEqualTo(fiToFICustomerCreditTransfer.getGroupHeader().getMessageIdentification());
                    assertThat(groupHeader91.getCreationDatetime()).
                            isEqualTo(fiToFICustomerCreditTransfer.getGroupHeader().getCreationDateTime());
                    assertThat(groupHeader91.getInstructingAgent()).isEqualTo(instructingAgent);
                    assertThat(groupHeader91.getInstructedAgent()).isEqualTo(instructedAgent);});

    }

}
