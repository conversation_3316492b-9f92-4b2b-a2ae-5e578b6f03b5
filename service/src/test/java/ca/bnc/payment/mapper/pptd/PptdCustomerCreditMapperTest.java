package ca.bnc.payment.mapper.pptd;


import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.CreditTransferTransaction39;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.FIToFICustomerCreditTransfer;
import ca.bnc.payment.pmt_outgoing_payment_management_pptd_resources.generated.model.GroupHeader93;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.FIToFICustomerCreditTransferV08;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class PptdCustomerCreditMapperTest {

    private PptdCustomerCreditTransferMapper testee;

    @Mock
    private PptdGroupHeaderMapper pptdGroupHeaderMapper;
    @Mock
    private PptdCreditTransferTransactionInformationMapper pptdCreditTransferTransactionInformationMapper;

    @BeforeEach
    void setup() {
        testee = new PptdCustomerCreditTransferMapper(pptdGroupHeaderMapper, pptdCreditTransferTransactionInformationMapper);
    }

    @Test
    void map() {
        FIToFICustomerCreditTransferV08 creditorIdentification = Instancio.create(FIToFICustomerCreditTransferV08.class);
        String confirmationId = "confirmationId";
        GroupHeader93 groupHeader93 = Instancio.create(GroupHeader93.class);
        given(pptdGroupHeaderMapper.mapGroupHeader(creditorIdentification.getGroupHeader())).willReturn(groupHeader93);
        List<CreditTransferTransaction39> creditTransferTransaction39 = Instancio.createList(CreditTransferTransaction39.class);
        given(pptdCreditTransferTransactionInformationMapper.mapCreditTransferTransactionInformation(creditorIdentification.getCreditTransferTransactionInformation(), confirmationId)).willReturn(creditTransferTransaction39);

        FIToFICustomerCreditTransfer customerCreditTransfer = testee.mapCustomerCreditTransfer(creditorIdentification, confirmationId);

        assertThat(customerCreditTransfer.getGroupHeader()).isEqualTo(groupHeader93);
        assertThat(customerCreditTransfer.getCreditTransferTransactionInformation()).isEqualTo(creditTransferTransaction39);
    }

}
