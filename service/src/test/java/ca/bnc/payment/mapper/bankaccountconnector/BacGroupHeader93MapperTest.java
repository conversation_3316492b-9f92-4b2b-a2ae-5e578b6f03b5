package ca.bnc.payment.mapper.bankaccountconnector;

import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.GroupHeader;
import ca.nbc.payment.etransfer.bankaccountconnector.model.BranchAndFinancialInstitutionIdentification6;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ClearingSystemIdentification3ChoiceProprietary;
import ca.nbc.payment.etransfer.bankaccountconnector.model.GroupHeader93;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementInstruction7;
import ca.nbc.payment.etransfer.bankaccountconnector.model.SettlementMethod1Code;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BacGroupHeader93MapperTest {

    private BacGroupHeader93Mapper mapper;

    @Mock
    private BacBranchAndFinancialInstitutionIdentification6Mapper bacBranchAndFinancialInstitutionIdentification6Mapper;
    @Mock
    private BacSettlementInstruction7Mapper bacSettlementInstruction7Mapper;

    private static final String NUMBER_OF_TRANSACTIONS = "1";
    private static final String INSTRUCTING_AGENT_MEMBER_IDENTIFICATION = "CA000612";
    private static final String INSTRUCTED_AGENT_MEMBER_IDENTIFICATION = "NOTPROVIDED";

    @BeforeEach
    void setUp() {
        mapper = new BacGroupHeader93Mapper(
                bacSettlementInstruction7Mapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                bacSettlementInstruction7Mapper,
                bacBranchAndFinancialInstitutionIdentification6Mapper);
    }

    @Test
    void givenGroupHeader_whenMap_thenReturnBankAccountConnectorEquivalent() {
        GroupHeader groupHeader = Instancio.create(GroupHeader.class);
        BranchAndFinancialInstitutionIdentification6 instructingAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        BranchAndFinancialInstitutionIdentification6 instructedAgent = Instancio.create(BranchAndFinancialInstitutionIdentification6.class);
        SettlementInstruction7 settlementInstruction = new SettlementInstruction7()
                .settlementMethod(SettlementMethod1Code.CLRG)
                .clearingSystem(new ClearingSystemIdentification3ChoiceProprietary()
                        .proprietary(ClearingSystemIdentification3ChoiceProprietary.ProprietaryEnum.ETR));

        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTING_AGENT_MEMBER_IDENTIFICATION)).willReturn(instructingAgent);
        given(bacBranchAndFinancialInstitutionIdentification6Mapper.map(INSTRUCTED_AGENT_MEMBER_IDENTIFICATION)).willReturn(instructedAgent);
        given(bacSettlementInstruction7Mapper.map()).willReturn(settlementInstruction);

        GroupHeader93 result = mapper.map(groupHeader);

        assertThat(result).isNotNull();
        assertThat(result.getMessageIdentification()).isEqualTo(groupHeader.getMessageIdentification());
        assertThat(result.getCreationDatetime()).isEqualTo(groupHeader.getCreationDateTime());
        assertThat(result.getNumberOfTransactions()).isEqualTo(NUMBER_OF_TRANSACTIONS);
        assertThat(result.getSettlementInformation()).isEqualTo(settlementInstruction);
        assertThat(result.getInstructingAgent()).isEqualTo(instructingAgent);
        assertThat(result.getInstructedAgent()).isEqualTo(instructedAgent);
    }
}
