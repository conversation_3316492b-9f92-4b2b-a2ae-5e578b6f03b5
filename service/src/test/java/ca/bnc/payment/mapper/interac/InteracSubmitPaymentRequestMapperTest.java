package ca.bnc.payment.mapper.interac;

import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentRequest;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class InteracSubmitPaymentRequestMapperTest {

    private InteracSubmitPaymentRequestMapper testee;

    @BeforeEach
    void setUp() {
        testee = new InteracSubmitPaymentRequestMapper();
    }

    @Test
    void givenSendPaymentRequestAndInitiatePaymentResponse_whenMap_thenReturnSubmitPaymentRequest() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest sendPaymentRequest =
                Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest.class);
        String clearingSystemReference = "clearingSystemReference";

        SubmitPaymentRequest result = testee.map(sendPaymentRequest, clearingSystemReference);

        assertThat(result.getTransactionId()).isEqualTo(sendPaymentRequest
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification());
        assertThat(result.getClearingSystemReference()).isEqualTo(clearingSystemReference);
        assertThat(result.getParticipantTransactionDate()).isEqualTo(sendPaymentRequest
                .getFiToFICustomerCreditTransfer()
                .getGroupHeader()
                .getCreationDateTime());
        assertThat(result.getNonRealtimeProcessingAllowed()).isNull();

    }

}
