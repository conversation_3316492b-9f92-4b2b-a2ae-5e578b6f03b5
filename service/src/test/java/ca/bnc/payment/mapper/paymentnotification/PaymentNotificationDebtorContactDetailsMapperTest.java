package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.DebtorContact;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class PaymentNotificationDebtorContactDetailsMapperTest {

    private PaymentNotificationDebtorContactDetailsMapper testee;

    @BeforeEach
    void setup() {
        testee = new PaymentNotificationDebtorContactDetailsMapper();
    }

    @Test
    void map() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorContact expected
                = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DebtorContact.class);

        DebtorContact actual = testee.mapContactDetails(expected);

        assertThat(actual).isNotNull();
        assertThat(actual.getMobileNumber()).isEqualTo(expected.getMobileNumber());
        assertThat(actual.getEmailAddress()).isEqualTo(expected.getEmailAddress());
    }
}