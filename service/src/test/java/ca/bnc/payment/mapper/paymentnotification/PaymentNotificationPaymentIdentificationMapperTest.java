package ca.bnc.payment.mapper.paymentnotification;

import ca.bnc.payment.pmt_outgoing_payment_management_payment_notification_resources.generated.model.PaymentIdentification7;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class PaymentNotificationPaymentIdentificationMapperTest {

    private PaymentNotificationPaymentIdentificationMapper testee;

    @BeforeEach
    void setup() {
        testee = new PaymentNotificationPaymentIdentificationMapper();
    }

    @Test
    void map() {
        ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7 outgoingPaymentIdentification
                = Instancio.create(ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.PaymentIdentification7.class);
        String confirmationId = "confirmationId";

        PaymentIdentification7 paymentIdentification = testee.mapPaymentIdentification(outgoingPaymentIdentification, confirmationId);

        assertThat(paymentIdentification.getInstructionIdentification()).isEqualTo(outgoingPaymentIdentification.getInstructionIdentification());
    }
}