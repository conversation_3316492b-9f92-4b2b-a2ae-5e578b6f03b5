package ca.bnc.payment.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ErrorUtilTest {

    private ErrorUtil testee;

    @BeforeEach
    void setup() {
        testee = new ErrorUtil();
    }

    @ParameterizedTest
    @ValueSource(strings = {"1", "2", "3", "999", "5000", "5001", "5002", "5003"})
    void givenValueInList_whenCallIsInListMethod_thenReturnTrue(final String code) {
        assertThat(testee.isTechnicalErrorCode(code)).isTrue();
    }

    @Test
    void givenValueNotInList_whenCallIsInListMethod_thenReturnFalse() {
        assertThat(testee.isTechnicalErrorCode("0")).isFalse();
    }

}