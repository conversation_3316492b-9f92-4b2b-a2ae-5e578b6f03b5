package ca.bnc.payment.util;

import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticPaymentNotificationRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import ca.bnc.payment.utils.RequestDtoTestUtils;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class RequestDtoUtilTest {

    private static final RequestDto<SendPaymentRequest> sendRequestDto =
            Instancio.of(new TypeToken<RequestDto<SendPaymentRequest>>() {
            }).create();
    private static final RequestDto<DomesticCancellationPaymentRequest> canceRequestDto =
            Instancio.of(new TypeToken<RequestDto<DomesticCancellationPaymentRequest>>() {
            }).create();

    private static final RequestDto<DomesticPaymentNotificationRequest> reissueRequestDto =
            Instancio.of(new TypeToken<RequestDto<DomesticPaymentNotificationRequest>>() {
            }).create();

    private RequestDtoUtil testee;

    @BeforeEach
    void setUp() {
        testee = new RequestDtoUtil();
    }

    @Test
    void whenGetSendEndToEndIdentification_willReturn() {

        String actual = testee.getSendEndToEndIdentification(sendRequestDto);

        assertThat(actual).isEqualTo(sendRequestDto
                .getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification());
    }

    @Test
    void whenGetCancelEndToEndIdentification_willReturn() {

        String actual = testee.getCancelEndToEndIdentification(canceRequestDto);

        assertThat(actual).isEqualTo(canceRequestDto
                .getRequestBody()
                .getCreditTransferTransactionInformation()
                .getPaymentIdentification()
                .getEndToEndIdentification());
    }

    @Test
    void whenGetCancellationReasonForSendPayment_thenReturnCancellationReason() {

        String actual = testee.getCancellationReason(canceRequestDto);

        assertThat(actual).isEqualTo(canceRequestDto
                .getRequestBody()
                .getCreditTransferTransactionInformation()
                .getCancelReason());
    }

    @Test
    void whenGetReissueEndToEndIdentification_willReturn() {

        String actual = testee.getReissueEndToEndIdentification(reissueRequestDto);

        assertThat(actual).isEqualTo(reissueRequestDto
                .getRequestBody()
                .getEndToEndIdentification());
    }

    @Test
    void whenGetSendClientType_willReturn() {

        SupplementaryData.ClientTypeEnum actual = testee.getClientType(sendRequestDto);

        assertThat(actual).isEqualTo(sendRequestDto
                .getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .getClientType());
    }

    @ParameterizedTest(name = "ClientType = {0} , isAnOrganization = {1}")
    @MethodSource("organizationInputProvider")
    void whenIsAnOrganization_willReturnTrueOrFalse(final SupplementaryData.ClientTypeEnum clientType, final boolean expectedResult) {
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        requestDto.getRequestBody()
                .getFiToFICustomerCreditTransfer()
                .getCreditTransferTransactionInformation()
                .getSupplementaryData()
                .setClientType(clientType);
        assertThat(testee.isAnOrganization(requestDto)).isEqualTo(expectedResult);
    }

    private static Stream<Arguments> organizationInputProvider() {
        return Stream.of(
                Arguments.of(SupplementaryData.ClientTypeEnum.ORGANIZATION, TRUE),
                Arguments.of(SupplementaryData.ClientTypeEnum.INDIVIDUAL, FALSE)
        );
    }
}
