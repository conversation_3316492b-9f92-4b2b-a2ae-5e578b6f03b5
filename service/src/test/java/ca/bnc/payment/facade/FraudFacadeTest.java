package ca.bnc.payment.facade;

import ca.bnc.payment.adapter.FraudAdapter;
import ca.bnc.payment.mapper.fraud.FraudCheckRequestMapper;
import ca.bnc.payment.mapper.fraud.FraudHeaderMapper;
import ca.bnc.payment.mapper.fraud.FraudHeaderMapper.FraudHeader;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.pmt_outgoing_payment_management_fraud_resources.generated.model.FraudCheckRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_fraud_resources.generated.model.FraudCheckResponse;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class FraudFacadeTest {
    private FraudFacade fraudFacade;
    @Mock
    private FraudAdapter fraudAdapter;

    @Mock
    private FraudCheckRequestMapper fraudCheckRequestMapper;

    @Mock
    private FraudHeaderMapper fraudHeaderMapper;

    @BeforeEach
    void setUp() {
        fraudFacade = new FraudFacade(fraudAdapter, fraudHeaderMapper, fraudCheckRequestMapper);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(fraudAdapter, fraudHeaderMapper, fraudCheckRequestMapper);
    }

    @Test
    void getPartyClientByIdTest() {
        FraudCheckResponse fraudCheckResponse = Instancio.create(FraudCheckResponse.class);
        FraudHeader fraudHeader = Instancio.create(FraudHeader.class);
        FraudCheckRequest fraudCheckRequest = Instancio.create(FraudCheckRequest.class);
        SendPaymentContext sendPaymentContext = Instancio.create(SendPaymentContext.class);

        given(fraudCheckRequestMapper.map(sendPaymentContext)).willReturn(fraudCheckRequest);
        given(fraudAdapter.fraudPreventionOnlineFraudVerificationPost(fraudHeader, fraudCheckRequest))
                .willReturn(fraudCheckResponse);
        given(fraudHeaderMapper.map(sendPaymentContext)).willReturn(fraudHeader);

        final FraudCheckResponse result = fraudFacade.fraudPreventionOnlineFraudVerificationPost(sendPaymentContext);

        assertThat(result).isEqualTo(fraudCheckResponse);
        then(fraudAdapter).should().fraudPreventionOnlineFraudVerificationPost(fraudHeader, fraudCheckRequest);

    }
}
