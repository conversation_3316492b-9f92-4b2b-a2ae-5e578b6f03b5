package ca.bnc.payment.facade;

import ca.bnc.payment.adapter.PartyAdapter;
import ca.bnc.payment.exception.party.PartyApiException;
import ca.bnc.payment.exception.party.PartyApiTimeoutException;
import ca.bnc.payment.exception.repository.RepositoryException;
import ca.bnc.payment.handler.fallback.sendpayment.SendPaymentFallbackHandler;
import ca.bnc.payment.model.dto.RequestDto;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.util.FeignClientUtil;
import ca.bnc.payment.util.RequestDtoUtil;
import ca.bnc.payment.utils.RequestDtoTestUtils;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ca.nbc.payment.pmtpartnersparty.model.IdentifierStatus;
import ca.nbc.payment.pmtpartnersparty.model.Identifiers;
import ch.qos.logback.classic.Logger;
import feign.Request;
import feign.RetryableException;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static ca.bnc.payment.constant.PartyErrorMessage.PARTY_API_BAD_REQUEST;
import static ca.bnc.payment.utils.RequestDtoTestUtils.X_CHANNEL_ID;
import static ca.bnc.payment.utils.RequestDtoTestUtils.X_CLIENT_ID;
import static ca.bnc.payment.utils.RequestDtoTestUtils.X_REQUEST_ID;
import static ca.nbc.payment.pmtpartnersparty.model.IdentifierType.INTERAC_USER_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class PartyFacadeTest {

    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final String ACCEPT_VERSION = "v1";
    private final Logger logger = (Logger) LoggerFactory.getLogger(PartyFacade.class.getName());

    private PartyFacade instance;
    @Mock
    private PartyAdapter partyAdapter;
    @Mock
    private SendPaymentFallbackHandler sendPaymentFallbackHandler;
    @Mock
    private RequestDtoUtil requestDtoUtil;
    @Mock
    private FeignClientUtil feignClientUtil;
    @Mock
    private LoggingFacade loggingFacade;

    @BeforeEach
    void setup() {
        instance = new PartyFacade(partyAdapter, sendPaymentFallbackHandler, requestDtoUtil, feignClientUtil, loggingFacade);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(partyAdapter, sendPaymentFallbackHandler, requestDtoUtil, feignClientUtil, loggingFacade);
    }

    @Test
    void getPartyClientByIdTest() {
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        final Identifiers identifiers = Instancio.create(Identifiers.class);
        given(partyAdapter.getPartyClientById(X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE))
                .willReturn(identifiers);

        final Identifiers result = instance.getPartyClientById(requestDto);

        assertThat(result).isEqualTo(identifiers);
    }

    @ParameterizedTest
    @MethodSource("getPartyExceptions")
    void givenPartyException_whenGetPartyClientById_thenThrow(
            final Exception partyException,
            final Exception expectedException,
            final Boolean isSocketTimeout) {
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        given(partyAdapter.getPartyClientById(X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE))
                .willThrow(partyException);
        given(requestDtoUtil.getSendEndToEndIdentification(requestDto)).willReturn(END_TO_END_IDENTIFICATION);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(partyException)).willReturn(isSocketTimeoutValue)
        );

        assertThatThrownBy(() -> instance.getPartyClientById(requestDto))
                .usingRecursiveComparison()
                .isEqualTo(expectedException);

        then(sendPaymentFallbackHandler).should().handleClientIdentifierFailure(END_TO_END_IDENTIFICATION);
    }

    @ParameterizedTest
    @MethodSource("getPartyExceptions")
    void givenPartyExceptionAndCleanupException_whenGetPartyClientById_thenThrow(
            final Exception partyException,
            final Exception expectedException,
            final Boolean isSocketTimeout) {
        final RepositoryException cleanupException = new RepositoryException(new RuntimeException(), "cleanup exception");
        final SendPaymentRequest sendPaymentRequest = Instancio.create(SendPaymentRequest.class);
        final RequestDto<SendPaymentRequest> requestDto = RequestDtoTestUtils.createRequestDTO(sendPaymentRequest);
        willThrow(cleanupException).given(sendPaymentFallbackHandler).handleClientIdentifierFailure(END_TO_END_IDENTIFICATION);
        given(partyAdapter.getPartyClientById(X_CHANNEL_ID,
                ACCEPT_VERSION,
                X_REQUEST_ID.toString(),
                X_CLIENT_ID,
                List.of(INTERAC_USER_ID),
                IdentifierStatus.ACTIVE))
                .willThrow(partyException);
        given(requestDtoUtil.getSendEndToEndIdentification(requestDto)).willReturn(END_TO_END_IDENTIFICATION);
        Optional.ofNullable(isSocketTimeout).ifPresent(
                isSocketTimeoutValue -> given(feignClientUtil.isSocketTimeout(partyException)).willReturn(isSocketTimeoutValue)
        );

        assertThatThrownBy(() -> instance.getPartyClientById(requestDto)).isInstanceOfSatisfying(
                expectedException.getClass(),
                e -> assertThat(e).usingRecursiveComparison().isEqualTo(expectedException));
        then(loggingFacade).should().error(logger, cleanupException);
    }

    public static Stream<Arguments> getPartyExceptions() {
        return Stream.of(
                Arguments.of(
                        new RetryableException(-1,
                                "socket timeout exception",
                                Request.HttpMethod.GET,
                                new SocketTimeoutException(),
                                (Long) null,
                                Instancio.create(Request.class)),
                        new PartyApiTimeoutException(new SocketTimeoutException()),
                        Boolean.TRUE),
                Arguments.of(
                        new PartyApiTimeoutException(new RuntimeException()),
                        new PartyApiTimeoutException(new RuntimeException()),
                        null),
                Arguments.of(
                        new RetryableException(-1,
                                "other retryable exception",
                                Request.HttpMethod.GET,
                                new RuntimeException(),
                                (Long) null,
                                Instancio.create(Request.class)),
                        new PartyApiException(new RuntimeException(),
                                "Exception calling Party API"
                        ),
                        Boolean.FALSE),
                Arguments.of(
                        new RuntimeException(),
                        new PartyApiException(new RuntimeException(),
                                "Exception calling Party API"
                        ),
                        Boolean.FALSE),
                Arguments.of(
                        new PartyApiException(PARTY_API_BAD_REQUEST),
                        new PartyApiException(PARTY_API_BAD_REQUEST),
                        null)
        );
    }
}