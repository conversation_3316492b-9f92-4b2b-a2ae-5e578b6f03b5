package ca.bnc.payment.facade;

import ca.bnc.payment.adapter.InteracPaymentsAdapter;
import ca.bnc.payment.lib.interacsecurity.InteracTokenManager;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.AccountAliasType;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.CancelPaymentTransaction;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ChannelIndicator;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.InitiatePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentOptionResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPostRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.PaymentsCancelTransactionPutRequestModel;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ProductCode;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.ReversePaymentResponse;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SendTransferNotice;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentRequest;
import ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SubmitPaymentResponse;
import ca.bnc.payment.mapper.interac.InteracChannelIndicatorMapper;
import ca.bnc.payment.mapper.interac.InteracInitiatePaymentRequestMapper;
import ca.bnc.payment.mapper.interac.InteracPaymentsCancelTransactionPostRequestModelMapper;
import ca.bnc.payment.mapper.interac.InteracPaymentsCancelTransactionPutRequestModelMapper;
import ca.bnc.payment.mapper.interac.InteracSubmitPaymentRequestMapper;
import ca.bnc.payment.model.task.CancelPaymentContext;
import ca.bnc.payment.model.task.ReissuePaymentContext;
import ca.bnc.payment.model.task.SendPaymentContext;
import ca.bnc.payment.model.task.TaskContext;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.CreditorContact;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticCancellationPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.DomesticPaymentNotificationRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SendPaymentRequest;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.SupplementaryData;
import ca.bnc.payment.util.IdGenerator;
import ca.bnc.payment.util.InteracUtil;
import ca.bnc.payment.util.RequestDtoUtil;
import ca.bnc.payment.util.TimeService;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.util.NoSuchElementException;
import java.util.stream.Stream;

import static ca.bnc.payment.lib.pmt_payment_interac_contract.generated.model.SignatureType.PAYLOAD_DIGEST_SHA256;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracPaymentsFacadeTest {

    private static final String N_A = "N/A";
    private static final String PAYMENT_PARTICIPANT_ID = Instancio.create(String.class);
    private static final String PAYMENT_INDIRECT_CONNECTOR_ID = Instancio.create(String.class);
    private static final String END_TO_END_IDENTIFICATION = Instancio.create(String.class);
    private static final String CANCELLATION_REASON = Instancio.create(String.class);
    private static final String TOKEN = Instancio.create(String.class);
    private static final String PAYLOAD = Instancio.create(String.class);
    private static final ChannelIndicator CHANNEL_INDICATOR = Instancio.create(ChannelIndicator.class);
    private static final OffsetDateTime NOW = Instancio.create(OffsetDateTime.class);
    private static final TaskContext<SendPaymentRequest> TASK_CONTEXT = Instancio.of(new TypeToken<TaskContext<SendPaymentRequest>>() {
            })
            .create();
    private static final SupplementaryData.ClientTypeEnum CLIENT_TYPE = Instancio.create(SupplementaryData.ClientTypeEnum.class);
    private static final String TXN_ID = "TXN_ID";
    private static final String PHONE_NUMBER = Instancio.create(String.class);
    private static final String EMAIL_ADDRESS = Instancio.create(String.class);

    private final CancelPaymentContext cancelPaymentContext = Instancio.create(CancelPaymentContext.class);

    private final ReissuePaymentContext reissuePaymentContext = Instancio.create(ReissuePaymentContext.class);

    private final Logger logger = (Logger) LoggerFactory.getLogger(InteracPaymentsFacade.class.getName());

    private InteracPaymentsFacade instance;

    @Mock
    private LoggingFacade loggingFacade;
    @Mock
    private InteracTokenManager interacTokenManager;
    @Mock
    private InteracPaymentsAdapter interacPaymentsAdapter;
    @Mock
    private InteracChannelIndicatorMapper interacChannelIndicatorMapper;
    @Mock
    private InteracInitiatePaymentRequestMapper interacInitiatePaymentRequestMapper;
    @Mock
    private InteracSubmitPaymentRequestMapper interacSubmitPaymentRequestMapper;
    @Mock
    private InteracPaymentsCancelTransactionPostRequestModelMapper interacPaymentsCancelTransactionPostRequestModelMapper;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private InteracPaymentsCancelTransactionPutRequestModelMapper interacPaymentsCancelTransactionPutRequestModelMapper;
    @Mock
    private RequestDtoUtil requestDtoUtil;
    @Mock
    private SendPaymentContext sendPaymentContext;
    @Mock
    private TimeService timeService;
    @Mock
    private InteracUtil interacUtil;
    @Mock
    private IdGenerator idGenerator;

    @Captor
    private ArgumentCaptor<String> tokenCaptor;

    @BeforeEach
    void setup() {
        instance = new InteracPaymentsFacade(
                loggingFacade,
                interacTokenManager,
                interacPaymentsAdapter,
                interacChannelIndicatorMapper,
                interacInitiatePaymentRequestMapper,
                interacSubmitPaymentRequestMapper,
                interacPaymentsCancelTransactionPostRequestModelMapper,
                objectMapper,
                interacPaymentsCancelTransactionPutRequestModelMapper,
                requestDtoUtil,
                timeService,
                interacUtil,
                idGenerator);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(
                loggingFacade,
                interacTokenManager,
                interacPaymentsAdapter,
                interacChannelIndicatorMapper,
                interacInitiatePaymentRequestMapper,
                interacSubmitPaymentRequestMapper,
                interacPaymentsCancelTransactionPostRequestModelMapper,
                interacPaymentsCancelTransactionPutRequestModelMapper,
                objectMapper,
                requestDtoUtil,
                sendPaymentContext,
                timeService,
                interacUtil,
                idGenerator);
    }

    @Test
    void givenSuccessScenario_whenInitiatePayment_thenResponseReturned() throws JsonProcessingException {

        final InitiatePaymentRequest initiatePaymentRequest = Instancio.create(InitiatePaymentRequest.class);
        final InitiatePaymentResponse initiatePaymentResponse = Instancio.create(InitiatePaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacInitiatePaymentRequestMapper.map(TASK_CONTEXT.getRequestBody(), TASK_CONTEXT.getActiveIdentifier())).willReturn(initiatePaymentRequest);
        given(objectMapper.writeValueAsString(initiatePaymentRequest)).willReturn(PAYLOAD);
        given(interacTokenManager.getAccessToken(PAYLOAD, NOW)).willReturn(TOKEN);
        given(requestDtoUtil.getClientType(TASK_CONTEXT.getRequestDto())).willReturn(CLIENT_TYPE);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getIndirectConnectorIdForHeader(CLIENT_TYPE)).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.initiatePayment(
                PAYMENT_PARTICIPANT_ID,
                TASK_CONTEXT.getActiveIdentifier().getValue(),
                TASK_CONTEXT.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                TASK_CONTEXT.getRetryIndicator(),
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                initiatePaymentRequest
        )).willReturn(initiatePaymentResponse);

        final InitiatePaymentResponse actualResponse = instance.initiatePayment(sendPaymentContext);

        assertThat(actualResponse).isEqualTo(initiatePaymentResponse);
    }


    @Test
    void givenInvalidHttpPayload_whenInitiatePayment_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final InitiatePaymentRequest initiatePaymentRequest = Instancio.create(InitiatePaymentRequest.class);
        final InitiatePaymentResponse initiatePaymentResponse = Instancio.create(InitiatePaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacInitiatePaymentRequestMapper.map(TASK_CONTEXT.getRequestBody(), TASK_CONTEXT.getActiveIdentifier())).willReturn(initiatePaymentRequest);
        given(objectMapper.writeValueAsString(initiatePaymentRequest)).willThrow(JsonProcessingException.class);
        given(requestDtoUtil.getClientType(TASK_CONTEXT.getRequestDto())).willReturn(CLIENT_TYPE);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getIndirectConnectorIdForHeader(CLIENT_TYPE)).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.initiatePayment(
                eq(PAYMENT_PARTICIPANT_ID),
                eq(TASK_CONTEXT.getActiveIdentifier().getValue()),
                eq(TASK_CONTEXT.getRequestId()),
                eq(CHANNEL_INDICATOR),
                tokenCaptor.capture(),
                eq(PAYLOAD_DIGEST_SHA256),
                eq(NOW),
                eq(TASK_CONTEXT.getRetryIndicator()),
                eq(PAYMENT_INDIRECT_CONNECTOR_ID),
                eq(null),
                eq(initiatePaymentRequest)
        )).willReturn(initiatePaymentResponse);

        final InitiatePaymentResponse actualResponse = instance.initiatePayment(sendPaymentContext);

        assertThat(actualResponse).isEqualTo(initiatePaymentResponse);
        assertThat(tokenCaptor.getValue()).isEqualTo(StringUtils.SPACE);

        then(loggingFacade).should().error(logger, N_A);
    }

    @Test
    void givenSuccessScenario_whenReversePayment_thenSuccess() throws JsonProcessingException {
        final ReversePaymentResponse reversePaymentResponse = Instancio.create(ReversePaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getRequestDto().getXChannelType().getValue())).willReturn(CHANNEL_INDICATOR);
        given(requestDtoUtil.getSendEndToEndIdentification(TASK_CONTEXT.getRequestDto())).willReturn(END_TO_END_IDENTIFICATION);
        given(interacTokenManager.getAccessToken(null, NOW)).willReturn(TOKEN);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.reversePayment(
                PAYMENT_PARTICIPANT_ID,
                TASK_CONTEXT.getActiveIdentifier().getValue(),
                TASK_CONTEXT.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                END_TO_END_IDENTIFICATION,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null
        )).willReturn(reversePaymentResponse);

        final ReversePaymentResponse actualResponse = instance.reversePayment(sendPaymentContext);

        then(objectMapper).should().writeValueAsString(null);
        assertThat(actualResponse).isEqualTo(reversePaymentResponse);
    }

    @Test
    void givenInvalidHttpPayload_whenReversePayment_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final ReversePaymentResponse reversePaymentResponse = Instancio.create(ReversePaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getRequestDto().getXChannelType().getValue())).willReturn(CHANNEL_INDICATOR);
        given(requestDtoUtil.getSendEndToEndIdentification(TASK_CONTEXT.getRequestDto())).willReturn(END_TO_END_IDENTIFICATION);
        given(objectMapper.writeValueAsString(null)).willThrow(JsonProcessingException.class);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.reversePayment(
                eq(PAYMENT_PARTICIPANT_ID),
                eq(TASK_CONTEXT.getActiveIdentifier().getValue()),
                eq(TASK_CONTEXT.getRequestId()),
                eq(CHANNEL_INDICATOR),
                tokenCaptor.capture(),
                eq(PAYLOAD_DIGEST_SHA256),
                eq(NOW),
                eq(END_TO_END_IDENTIFICATION),
                eq(PAYMENT_INDIRECT_CONNECTOR_ID),
                eq(null)
        )).willReturn(reversePaymentResponse);

        final ReversePaymentResponse actualResponse = instance.reversePayment(sendPaymentContext);

        assertThat(actualResponse).isEqualTo(reversePaymentResponse);
        assertThat(tokenCaptor.getValue()).isEqualTo(StringUtils.SPACE);

        then(loggingFacade).should().error(logger, N_A);
    }

    @Test
    void givenSuccessScenario_whenSubmitPayment_thenResponseReturned() throws JsonProcessingException {
        final String paymentTransactionToken = Instancio.create(String.class);
        final String clearingSystemReference = Instancio.create(String.class);
        final SubmitPaymentRequest submitPaymentRequest = Instancio.create(SubmitPaymentRequest.class);
        final SubmitPaymentResponse submitPaymentResponse = Instancio.create(SubmitPaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(sendPaymentContext.getInitiateInteracPaymentResponsePaymentTransactionToken()).willReturn(paymentTransactionToken);
        given(sendPaymentContext.getInitiateInteracPaymentResponseETransferId()).willReturn(clearingSystemReference);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacSubmitPaymentRequestMapper.map(TASK_CONTEXT.getRequestBody(), clearingSystemReference)).willReturn(submitPaymentRequest);
        given(objectMapper.writeValueAsString(submitPaymentRequest)).willReturn(PAYLOAD);
        given(interacTokenManager.getAccessToken(PAYLOAD, NOW)).willReturn(TOKEN);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.submitPayment(
                PAYMENT_PARTICIPANT_ID,
                TASK_CONTEXT.getActiveIdentifier().getValue(),
                TASK_CONTEXT.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                TASK_CONTEXT.getRetryIndicator(),
                paymentTransactionToken,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                submitPaymentRequest
        )).willReturn(submitPaymentResponse);

        final SubmitPaymentResponse actualResponse = instance.submitPayment(sendPaymentContext);

        assertThat(actualResponse).isEqualTo(submitPaymentResponse);
    }

    @Test
    void givenInvalidHttpPayload_whenSubmitPayment_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final String paymentTransactionToken = Instancio.create(String.class);
        final String clearingSystemReference = Instancio.create(String.class);
        final SubmitPaymentRequest submitPaymentRequest = Instancio.create(SubmitPaymentRequest.class);
        final SubmitPaymentResponse submitPaymentResponse = Instancio.create(SubmitPaymentResponse.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(sendPaymentContext.getTaskContext()).willReturn(TASK_CONTEXT);
        given(sendPaymentContext.getInitiateInteracPaymentResponsePaymentTransactionToken()).willReturn(paymentTransactionToken);
        given(sendPaymentContext.getInitiateInteracPaymentResponseETransferId()).willReturn(clearingSystemReference);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacSubmitPaymentRequestMapper.map(TASK_CONTEXT.getRequestBody(), clearingSystemReference)).willReturn(submitPaymentRequest);
        given(objectMapper.writeValueAsString(submitPaymentRequest)).willThrow(JsonProcessingException.class);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.submitPayment(
                eq(PAYMENT_PARTICIPANT_ID),
                eq(TASK_CONTEXT.getActiveIdentifier().getValue()),
                eq(TASK_CONTEXT.getRequestId()),
                eq(CHANNEL_INDICATOR),
                tokenCaptor.capture(),
                eq(PAYLOAD_DIGEST_SHA256),
                eq(NOW),
                eq(TASK_CONTEXT.getRetryIndicator()),
                eq(paymentTransactionToken),
                eq(PAYMENT_INDIRECT_CONNECTOR_ID),
                eq(null),
                eq(submitPaymentRequest)
        )).willReturn(submitPaymentResponse);

        final SubmitPaymentResponse actualResponse = instance.submitPayment(sendPaymentContext);

        assertThat(actualResponse).isEqualTo(submitPaymentResponse);
        assertThat(tokenCaptor.getValue()).isEqualTo(StringUtils.SPACE);

        then(loggingFacade).should().error(logger, N_A);
    }

    @Test
    void givenSuccessScenario_whenTransferCancelBegin_thenResponseReturned() throws JsonProcessingException {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final PaymentsCancelTransactionPostRequestModel paymentsCancelTransactionPostRequestModel =
                new PaymentsCancelTransactionPostRequestModel()
                        .txnId(END_TO_END_IDENTIFICATION)
                        .reasonMemo(CANCELLATION_REASON);
        final CancelPaymentTransaction cancelPaymentTransaction = Instancio.create(CancelPaymentTransaction.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(requestDtoUtil.getCancelEndToEndIdentification(taskContext.getRequestDto())).willReturn(END_TO_END_IDENTIFICATION);
        given(requestDtoUtil.getCancellationReason(taskContext.getRequestDto())).willReturn(CANCELLATION_REASON);
        given(interacPaymentsCancelTransactionPostRequestModelMapper.map(END_TO_END_IDENTIFICATION, CANCELLATION_REASON))
                .willReturn(paymentsCancelTransactionPostRequestModel);
        given(objectMapper.writeValueAsString(paymentsCancelTransactionPostRequestModel)).willReturn(PAYLOAD);
        given(interacTokenManager.getAccessToken(PAYLOAD, NOW)).willReturn(TOKEN);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.transferCancelBegin(
                PAYMENT_PARTICIPANT_ID,
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                etransferId,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                paymentsCancelTransactionPostRequestModel
        )).willReturn(cancelPaymentTransaction);

        final CancelPaymentTransaction actualResponse = instance.transferCancelBegin(cancelPaymentContext);

        assertThat(actualResponse).isEqualTo(cancelPaymentTransaction);
        assertThat(cancelPaymentContext.getCancelTxnId()).isEqualTo(actualResponse.getCancelTxnId());
    }

    @Test
    void givenInvalidHttpPayload_whenTransferCancelBegin_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final PaymentsCancelTransactionPostRequestModel paymentsCancelTransactionPostRequestModel = Instancio.create(PaymentsCancelTransactionPostRequestModel.class);
        final CancelPaymentTransaction cancelPaymentTransaction = Instancio.create(CancelPaymentTransaction.class);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(requestDtoUtil.getCancelEndToEndIdentification(taskContext.getRequestDto())).willReturn(END_TO_END_IDENTIFICATION);
        given(requestDtoUtil.getCancellationReason(taskContext.getRequestDto())).willReturn(CANCELLATION_REASON);
        given(interacPaymentsCancelTransactionPostRequestModelMapper.map(END_TO_END_IDENTIFICATION, CANCELLATION_REASON))
                .willReturn(paymentsCancelTransactionPostRequestModel);
        given(objectMapper.writeValueAsString(paymentsCancelTransactionPostRequestModel)).willThrow(JsonProcessingException.class);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);

        given(interacPaymentsAdapter.transferCancelBegin(
                eq(PAYMENT_PARTICIPANT_ID),
                eq(taskContext.getActiveIdentifier().getValue()),
                eq(taskContext.getRequestId()),
                eq(CHANNEL_INDICATOR),
                tokenCaptor.capture(),
                eq(PAYLOAD_DIGEST_SHA256),
                eq(NOW),
                eq(etransferId),
                eq(PAYMENT_INDIRECT_CONNECTOR_ID),
                eq(null),
                eq(paymentsCancelTransactionPostRequestModel)
        )).willReturn(cancelPaymentTransaction);

        final CancelPaymentTransaction actualResponse = instance.transferCancelBegin(cancelPaymentContext);

        assertThat(actualResponse).isEqualTo(cancelPaymentTransaction);
        assertThat(tokenCaptor.getValue()).isEqualTo(StringUtils.SPACE);

        then(loggingFacade).should().error(logger, N_A);
    }

    @Test
    void givenSuccessScenario_whenTransferCancelRollback_thenResponseReturned() throws JsonProcessingException {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);

        assertThatCode(() -> instance.transferCancelRollback(cancelPaymentContext)).doesNotThrowAnyException();

        then(objectMapper).should().writeValueAsString(null);
        then(interacTokenManager).should().getAccessToken(null, NOW);
        then(interacPaymentsAdapter).should().transferCancelRollback(PAYMENT_PARTICIPANT_ID,
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                CHANNEL_INDICATOR,
                StringUtils.SPACE,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                etransferId,
                cancelPaymentContext.getCancelTxnId(),
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null
        );
    }

    @Test
    void givenSuccessScenario_whenTransferCommitBegin_thenResponseReturned() throws JsonProcessingException {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final OffsetDateTime trxDate = taskContext.getRequestBody().getGroupHeader().getCreationDateTime();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final PaymentsCancelTransactionPutRequestModel paymentsCancelTransactionPutRequestModel = Instancio.create(PaymentsCancelTransactionPutRequestModel.class);

        given(idGenerator.generateInstructionIdentification()).willReturn(TXN_ID);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(objectMapper.writeValueAsString(paymentsCancelTransactionPutRequestModel)).willReturn(PAYLOAD);
        given(interacTokenManager.getAccessToken(PAYLOAD, NOW)).willReturn(TOKEN);
        given(interacPaymentsCancelTransactionPutRequestModelMapper.map(TXN_ID, trxDate)).willReturn(paymentsCancelTransactionPutRequestModel);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);

        assertThatCode(() -> instance.transferCancelCommit(cancelPaymentContext)).doesNotThrowAnyException();

        then(interacPaymentsAdapter).should().transferCancelCommit(PAYMENT_PARTICIPANT_ID,
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                etransferId,
                cancelPaymentContext.getCancelTxnId(),
                cancelPaymentContext.getTaskContext().getRetryIndicator(),
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                paymentsCancelTransactionPutRequestModel);
    }

    @Test
    void givenInvalidHttpPayload_whenTransferCommitBegin_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final TaskContext<DomesticCancellationPaymentRequest> taskContext = cancelPaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final OffsetDateTime trxDate = taskContext.getRequestBody().getGroupHeader().getCreationDateTime();
        final PaymentsCancelTransactionPutRequestModel paymentsCancelTransactionPutRequestModel = Instancio.create(PaymentsCancelTransactionPutRequestModel.class);

        given(idGenerator.generateInstructionIdentification()).willReturn(TXN_ID);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacPaymentsCancelTransactionPutRequestModelMapper.map(TXN_ID, trxDate)).willReturn(paymentsCancelTransactionPutRequestModel);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(objectMapper.writeValueAsString(paymentsCancelTransactionPutRequestModel)).willThrow(JsonProcessingException.class);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);

        assertThatCode(() -> instance.transferCancelCommit(cancelPaymentContext)).doesNotThrowAnyException();

        then(interacPaymentsAdapter).should().transferCancelCommit(eq(PAYMENT_PARTICIPANT_ID),
                eq(taskContext.getActiveIdentifier().getValue()),
                eq(taskContext.getRequestId()),
                eq(CHANNEL_INDICATOR),
                tokenCaptor.capture(),
                eq(PAYLOAD_DIGEST_SHA256),
                eq(NOW),
                eq(etransferId),
                eq(cancelPaymentContext.getCancelTxnId()),
                eq(cancelPaymentContext.getTaskContext().getRetryIndicator()),
                eq(PAYMENT_INDIRECT_CONNECTOR_ID),
                eq(null),
                eq(paymentsCancelTransactionPutRequestModel));

        assertThat(tokenCaptor.getValue()).isEqualTo(StringUtils.SPACE);
        then(loggingFacade).should().error(logger, N_A);
    }

    @Test
    void givenSuccessScenario_whenReissuePaymentNotification_thenResponseReturned() throws JsonProcessingException {
        final TaskContext<DomesticPaymentNotificationRequest> taskContext = reissuePaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final SendTransferNotice sendTransferNotice = new SendTransferNotice();
        final String payload = "payload";

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(objectMapper.writeValueAsString(sendTransferNotice)).willReturn(payload);

        assertThatCode(() -> instance.reissuePaymentNotification(reissuePaymentContext)).doesNotThrowAnyException();

        then(interacTokenManager).should().getAccessToken(payload, NOW);
        then(interacPaymentsAdapter).should().reissuePaymentNotification(PAYMENT_PARTICIPANT_ID,
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                CHANNEL_INDICATOR,
                StringUtils.SPACE,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                etransferId,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                sendTransferNotice
        );
    }

    @Test
    void givenInvalidHttpPayload_whenReissuePaymentNotification_thenSuccessWithEmptyInteracToken() throws JsonProcessingException {
        final TaskContext<DomesticPaymentNotificationRequest> taskContext = reissuePaymentContext.getTaskContext();
        final String etransferId = taskContext.getOutgoingDomesticPayment().getETransferId();
        final SendTransferNotice sendTransferNotice = new SendTransferNotice();

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(taskContext.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(objectMapper.writeValueAsString(sendTransferNotice)).willThrow(JsonProcessingException.class);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);

        assertThatCode(() -> instance.reissuePaymentNotification(reissuePaymentContext)).doesNotThrowAnyException();

        then(interacPaymentsAdapter).should().reissuePaymentNotification(PAYMENT_PARTICIPANT_ID,
                taskContext.getActiveIdentifier().getValue(),
                taskContext.getRequestId(),
                CHANNEL_INDICATOR,
                StringUtils.SPACE,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                etransferId,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null,
                sendTransferNotice
        );

        then(loggingFacade).should().error(logger, N_A);
    }

    @ParameterizedTest
    @MethodSource("getCreditorContactTestCases")
    void givenSuccessScenario_whenGetPaymentOptions_thenResponseReturned(final CreditorContact creditorContact,
                                                                         final AccountAliasType depositType,
                                                                         final String emailOrPhone) throws JsonProcessingException {
        final PaymentOptionResponse paymentOptionResponse = Instancio.create(PaymentOptionResponse.class);
        TASK_CONTEXT.getRequestDto().getRequestBody().getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation().getCreditor().setContactDetails(creditorContact);

        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);
        given(objectMapper.writeValueAsString(null)).willReturn(null);
        given(interacTokenManager.getAccessToken(null, NOW)).willReturn(TOKEN);
        given(interacUtil.getPaymentParticipantId()).willReturn(PAYMENT_PARTICIPANT_ID);
        given(interacUtil.getPaymentIndirectConnectorId()).willReturn(PAYMENT_INDIRECT_CONNECTOR_ID);
        given(interacPaymentsAdapter.getPaymentOptions(
                PAYMENT_PARTICIPANT_ID,
                TASK_CONTEXT.getActiveIdentifier().getValue(),
                TASK_CONTEXT.getRequestId(),
                CHANNEL_INDICATOR,
                TOKEN,
                PAYLOAD_DIGEST_SHA256,
                NOW,
                ProductCode.DOMESTIC,
                depositType,
                emailOrPhone,
                PAYMENT_INDIRECT_CONNECTOR_ID,
                null
        )).willReturn(paymentOptionResponse);

        final PaymentOptionResponse actualResponse = instance.getPaymentOptions(TASK_CONTEXT.getRequestDto(), TASK_CONTEXT.getActiveIdentifier());

        assertThat(actualResponse).isEqualTo(paymentOptionResponse);
    }

    private static Stream<Arguments> getCreditorContactTestCases() {
        return Stream.of(
                Arguments.of(
                        new CreditorContact().mobileNumber(PHONE_NUMBER),
                        AccountAliasType.PHONE,
                        PHONE_NUMBER
                ),
                Arguments.of(
                        new CreditorContact().emailAddress(EMAIL_ADDRESS),
                        AccountAliasType.EMAIL,
                        EMAIL_ADDRESS
                )
        );
    }

    @Test
    void givenInvalidContactDetails_whenGetPaymentOptions_thenResponseReturned() throws JsonProcessingException {
        TASK_CONTEXT.getRequestDto().getRequestBody().getFiToFICustomerCreditTransfer().getCreditTransferTransactionInformation().getCreditor().setContactDetails(new CreditorContact());

        given(objectMapper.writeValueAsString(null)).willReturn(null);
        given(interacTokenManager.getAccessToken(null, NOW)).willReturn(TOKEN);
        given(timeService.getNowOffsetDateTime()).willReturn(NOW);
        given(interacChannelIndicatorMapper.map(TASK_CONTEXT.getChannelType())).willReturn(CHANNEL_INDICATOR);

        assertThatThrownBy(() -> instance.getPaymentOptions(TASK_CONTEXT.getRequestDto(), TASK_CONTEXT.getActiveIdentifier()))
                .isInstanceOfSatisfying(NoSuchElementException.class,
                        e -> {
                            assertThat(e).isNotNull();
                            assertThat(e.getMessage()).isEqualTo("Neither mobile number nor email address is provided");
                        });
    }

}
