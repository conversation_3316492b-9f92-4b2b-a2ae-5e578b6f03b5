package ca.bnc.payment.caching;

import ca.bnc.payment.client.callback.SendPaymentCallbackClient;
import ca.bnc.payment.config.AppConfig;
import ca.bnc.payment.service.provider.CallbackClientProvider;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import org.awaitility.Durations;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@SpringBootTest
@ActiveProfiles({"test-os", "short-spring-cache"})
@TestPropertySource(properties = {"SPRING_PROFILE_INCLUDE=logging-full,okta-default"})
class CallbackClientProviderCacheTest {

    private static final String URL = "http://localhost:8080";

    private final Logger logger = (Logger) LoggerFactory.getLogger(CallbackClientProvider.class.getName());
    private final ListAppender<ILoggingEvent> listAppender = new ListAppender<>();

    @Autowired
    CacheManager cacheManager;
    @Autowired
    CallbackClientProvider callbackClientProvider;

    @BeforeEach
    void setUp() {
        // Start the appender.
        listAppender.start();
        // Add the appender to the logger.
        logger.addAppender(listAppender);
        callbackClientProvider.createCallbackClient(URL);
    }

    @Test
    void givenCallbackClientServiceThatShouldBeCached_whenCreateCallbackClient_thenResultShouldBePutInCache() {
        SendPaymentCallbackClient callbackClient = callbackClientProvider.createCallbackClient(URL);
        
        assertThat(getCachedSendPaymentCallBackClient())
                .isPresent()
                .hasValue(callbackClient);
        await().atMost(Durations.TWO_SECONDS).until(() -> !listAppender.list.isEmpty());
    }

    private Optional<SendPaymentCallbackClient> getCachedSendPaymentCallBackClient() {
        return Optional.ofNullable(cacheManager.getCache(AppConfig.CALL_BACK_CLIENT_CACHE))
                .map(c -> c.get(CallbackClientProviderCacheTest.URL, SendPaymentCallbackClient.class));
    }
}
