package ca.bnc.payment.client.domesticcreditors;

import ca.bnc.payment.client.interceptor.InterceptorFactory;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class DomesticCreditorsApiOktaInterceptorTest {

    private static final String DOMESTIC_CREDITORS_TOKEN_NAME = "domesticCreditorsScope";

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private RequestInterceptor delegate;

    @Mock
    private InterceptorFactory interceptorFactory;

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(delegate, interceptorFactory);
    }

    @Test
    @DisplayName("should Create Correct Delegate When Using interceptor factory")
    void shouldCreateCorrectDelegateWhenUsingInterceptorFactory() {

        given(interceptorFactory.createBncInterceptor(DOMESTIC_CREDITORS_TOKEN_NAME))
                .willReturn(delegate);
        DomesticCreditorsApiOktaInterceptor instance = new DomesticCreditorsApiOktaInterceptor(interceptorFactory);
        instance.apply(requestTemplate);

        then(interceptorFactory).should().createBncInterceptor(DOMESTIC_CREDITORS_TOKEN_NAME);
        then(delegate).should().apply(requestTemplate);


    }
}