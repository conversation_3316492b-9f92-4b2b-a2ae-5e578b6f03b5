package ca.bnc.payment.client.interac;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.interac.IgnorableInteracException;
import ca.bnc.payment.exception.interac.InteracException;
import ca.bnc.payment.exception.interac.RetryableInteracException;
import ca.bnc.payment.util.ErrorUtil;
import feign.Request;
import feign.Response;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static ca.bnc.payment.constant.InteracErrorMessage.INTERAC_PAYMENT_API_COMMON_ERROR;
import static ca.bnc.payment.constant.InteracOperations.PATH_CANCEL;
import static ca.bnc.payment.constant.InteracOperations.PATH_CANCEL_TRANSACTION;
import static ca.bnc.payment.constant.InteracOperations.PATH_PAYMENT_OPTIONS;
import static ca.bnc.payment.constant.InteracOperations.PATH_REISSUE_NOTIFICATION;
import static ca.bnc.payment.constant.InteracOperations.PATH_SEND;
import static feign.Request.HttpMethod.DELETE;
import static feign.Request.HttpMethod.GET;
import static feign.Request.HttpMethod.POST;
import static feign.Request.HttpMethod.PUT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class InteracPaymentsApiDecoderTest {

    private static final String URL_SEND = "http://localhost:8080/payments/transaction";
    private static final String URL_CANCEL = "http://localhost:8080/cancel";
    private static final String URL_CANCEL_TRANSACTION = "http://localhost:8080/cancel/transaction";
    private static final String URL_PAYMENT_OPTIONS = "http://localhost:8080/payments/options";
    private static final String URL_UNKNOWN = "http://localhost:8080/unknown";
    private static final OutgoingPaymentException.Error ERROR = Instancio.create(OutgoingPaymentException.Error.class);

    private InteracPaymentsApiDecoder testee;

    @Mock
    private InteracPaymentsApiDecoderErrorsBuilder errorsBuilder;

    @Mock
    private ErrorUtil errorUtil;

    @Mock
    private InteracPathFinder pathFinder;


    @BeforeEach
    void setup() {
        testee = new InteracPaymentsApiDecoder(errorsBuilder, errorUtil, pathFinder);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(errorsBuilder, pathFinder);
    }

    @Test
    void givenInteracTechnicalSourceError_whenDecode_thenReturnInteracExceptionWithErrors() {

        Response responseMock = mock(Response.class);
        Request requestMock = mock(Request.class);
        given(responseMock.status()).willReturn(400);
        given(responseMock.request()).willReturn(requestMock);
        given(requestMock.httpMethod()).willReturn(POST);
        String notificationUri = "/payments/CAq7Q5ww/resend-notification";
        given(requestMock.url()).willReturn(notificationUri);
        given(pathFinder.getPath(notificationUri)).willReturn(PATH_REISSUE_NOTIFICATION, PATH_REISSUE_NOTIFICATION);

        given(errorsBuilder.buildErrorFromResponseBody(responseMock)).willReturn(ERROR);
        given(errorUtil.isTechnicalErrorCode(anyString())).willReturn(Boolean.TRUE);
        InteracException expectedException = new InteracException(INTERAC_PAYMENT_API_COMMON_ERROR, ERROR);

        final Exception actual = testee.decode(null, responseMock);

        assertThat(actual)
                .isInstanceOf(InteracException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
        then(pathFinder).should(times(2)).getPath(notificationUri);
    }

    @ParameterizedTest
    @MethodSource({"badRequest", "notFound"})
    void givenSourceError_whenDecode_thenReturnInteracExceptionWithErrors(final String exceptionMessage,
                                                                          final Response response, final String url,
                                                                          final String expectedPath) {

        given(errorsBuilder.buildErrorFromResponseBody(response)).willReturn(ERROR);
        InteracException expectedException = new InteracException(exceptionMessage, ERROR);
        given(pathFinder.getPath(url)).willReturn(expectedPath);
        Exception actual = testee.decode(null, response);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);

        assertThat(actual).isInstanceOf(InteracException.class);
        assertThat(actual.getMessage()).isEqualTo(String.format("%s - errors: [%s]", exceptionMessage, ERROR.toString()));

    }

    @ParameterizedTest
    @MethodSource({"forbidden", "unauthorized", "internalError", "defaultCase"})
    void givenSourceError_whenDecode_thenReturnInteracExceptionWithoutErrors(final String exceptionMessage,
                                                                             final Response response, final String url,
                                                                             final String expectedPath) {

        InteracException expectedException = new InteracException(exceptionMessage);
        given(pathFinder.getPath(url)).willReturn(expectedPath);
        Exception actual = testee.decode(null, response);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);

        assertThat(actual).isInstanceOf(InteracException.class);
        assertThat(actual.getMessage()).isEqualTo(exceptionMessage);

    }

    @ParameterizedTest
    @MethodSource("unavailable")
    void givenSourceError_whenDecode_thenReturnRetryableInteracExceptionWithErrors(final String exceptionMessage,
                                                                                   final Response response, final String url,
                                                                                   final String expectedPath) {

        given(errorsBuilder.buildErrorFromResponseBody(response)).willReturn(ERROR);
        RetryableInteracException expectedException = new RetryableInteracException(exceptionMessage, ERROR);
        given(pathFinder.getPath(url)).willReturn(expectedPath);

        Exception actual = testee.decode(null, response);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);

        assertThat(actual).isInstanceOf(RetryableInteracException.class);
        assertThat(actual.getMessage()).isEqualTo(String.format("%s - errors: [%s]", exceptionMessage, ERROR.toString()));

    }

    @ParameterizedTest
    @MethodSource("throttling")
    void givenSourceError_whenDecode_thenReturnRetryableInteracExceptionWithoutErrors(final String exceptionMessage,
                                                                                      final Response response, final String url,
                                                                                      final String expectedPath) {

        InteracException expectedException = new InteracException(exceptionMessage);
        given(pathFinder.getPath(url)).willReturn(expectedPath);

        Exception actual = testee.decode(null, response);

        assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);

        assertThat(actual).isInstanceOf(InteracException.class);
        assertThat(actual.getMessage()).isEqualTo(exceptionMessage);

    }

    @ParameterizedTest(name = "http_status_code = {0} , expected_message = {1}")
    @MethodSource("ignorableExceptionHttpCodes")
    void givenGetPaymentOptionsIgnorableExceptions_whenDecode_thenReturnIgnorableInteracExceptionWithoutErrors(
            final int httpStatusCode, final String expectedErrorMessage) {
        IgnorableInteracException expectedException = new IgnorableInteracException(expectedErrorMessage);
        given(pathFinder.getPath(URL_PAYMENT_OPTIONS)).willReturn("payments/options");
        Map<String, Collection<String>> headers = Instancio.ofMap(new TypeToken<String>() {
        }, new TypeToken<Collection<String>>() {
        }).create();
        Request.Body body = Instancio.create(Request.Body.class);
        Response response = Response.builder()
                .request(Request.create(GET, URL_PAYMENT_OPTIONS, headers, body, null))
                .status(httpStatusCode)
                .build();

        Exception actual = testee.decode(null, response);

        assertThat(actual).isInstanceOf(IgnorableInteracException.class)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    private static Stream<Arguments> throttling() {

        return buildStreamArguments(List.of(
                        "The Interac Payments API blocked the INITIATE PAYMENT request call due to rate limiting",
                        "The Interac Payments API blocked the STOP PAYMENT request call due to rate limiting",
                        "The Interac Payments API blocked the SUBMIT PAYMENT request call due to rate limiting",
                        "The Interac Payments API blocked the INITIATE CANCELLATION request call due to rate limiting",
                        "The Interac Payments API blocked the INITIATE ROLLBACK request call due to rate limiting",
                        "The Interac Payments API blocked the GET PAYMENT OPTIONS request call due to rate limiting",
                        "The Interac Payments API blocked the REISSUE TRANSFER NOTIFICATION request call due to rate limiting"),
                429);

    }

    private static Stream<Arguments> buildStreamArguments(final List<String> messages, final int status) {

        Request.Body body = Instancio.create(Request.Body.class);
        Map<String, Collection<String>> headers = Instancio.ofMap(
                new TypeToken<String>() {
                },
                new TypeToken<Collection<String>>() {
                }).create();

        return Stream.of(
                Arguments.of(messages.get(0),
                        Response.builder()
                                .request(Request.create(POST, URL_SEND, headers, body, null))
                                .status(status)
                                .build(),
                        URL_SEND,
                        PATH_SEND
                ),
                Arguments.of(messages.get(1),
                        Response.builder()
                                .request(Request.create(DELETE, URL_SEND, headers, body, null))
                                .status(status)
                                .build(),
                        URL_SEND,
                        PATH_SEND),
                Arguments.of(messages.get(2),
                        Response.builder()
                                .request(Request.create(PUT, URL_SEND, headers, body, null))
                                .status(status)
                                .build(),
                        URL_SEND,
                        PATH_SEND),
                Arguments.of(messages.get(3),
                        Response.builder()
                                .request(Request.create(POST, URL_CANCEL, headers, body, null))
                                .status(status)
                                .build(),
                        URL_CANCEL,
                        PATH_CANCEL),
                Arguments.of(messages.get(4),
                        Response.builder()
                                .request(Request.create(DELETE, URL_CANCEL_TRANSACTION, headers, body, null))
                                .status(status)
                                .build(),
                        URL_CANCEL_TRANSACTION,
                        PATH_CANCEL_TRANSACTION),
                Arguments.of(messages.get(5),
                        Response.builder()
                                .request(Request.create(GET, URL_PAYMENT_OPTIONS, headers, body, null))
                                .status(status)
                                .build(),
                        URL_PAYMENT_OPTIONS,
                        PATH_PAYMENT_OPTIONS),
                Arguments.of(null,
                        Response.builder()
                                .request(Request.create(PUT, URL_UNKNOWN, headers, body, null))
                                .status(status)
                                .build(),
                        URL_UNKNOWN,
                        "/unknown"
                )
        );

    }

    private static Stream<Arguments> badRequest() {

        return buildStreamArguments(List.of(
                        "Bad INITIATE PAYMENT request received by the Interac Payments API",
                        "Bad STOP PAYMENT request received by the Interac Payments API",
                        "Bad SUBMIT PAYMENT request received by the Interac Payments API",
                        "Bad INITIATE CANCELLATION request received by the Interac Payments API",
                        "Bad INITIATE ROLLBACK request received by the Interac Payments API",
                        "Bad GET PAYMENT OPTIONS request received by the Interac Payments API",
                        "Bad REISSUE TRANSFER NOTIFICATION request received by the Interac Payments API"
                ),
                400);

    }

    private static Stream<Arguments> notFound() {

        return buildStreamArguments(List.of(
                        "Resource not found while executing the Interac INITIATE PAYMENT request",
                        "Resource not found while executing the Interac STOP PAYMENT request",
                        "Resource not found while executing the Interac SUBMIT PAYMENT request",
                        "Resource not found while executing the Interac INITIATE CANCELLATION request",
                        "Resource not found while executing the Interac INITIATE ROLLBACK request",
                        "Resource not found while executing the Interac GET PAYMENT OPTIONS request",
                        "Resource not found while executing the Interac REISSUE TRANSFER NOTIFICATION request"),
                404);

    }

    private static Stream<Arguments> forbidden() {
        return buildStreamArguments(List.of(
                        "The Interac Payments API refused to execute the INITIATE PAYMENT request",
                        "The Interac Payments API refused to execute the STOP PAYMENT request",
                        "The Interac Payments API refused to execute the SUBMIT PAYMENT request",
                        "The Interac Payments API refused to execute the INITIATE CANCELLATION request",
                        "The Interac Payments API refused to execute the INITIATE ROLLBACK request",
                        "The Interac Payments API refused to execute the GET PAYMENT OPTIONS request"),
                403);
    }

    private static Stream<Arguments> unauthorized() {
        Request.Body body = Instancio.create(Request.Body.class);
        Map<String, Collection<String>> headers = Instancio.ofMap(
                new TypeToken<String>() {
                },
                new TypeToken<Collection<String>>() {
                }).create();

        return Stream.of(
                Arguments.of("The service wants to get a protected resource from the Interac Payments API without providing the proper " +
                                "authorization",
                        Response.builder()
                                .request(Request.create(POST, URL_SEND, headers, body, null))
                                .status(401)
                                .build(),
                        URL_SEND,
                        PATH_SEND)
        );
    }

    private static Stream<Arguments> defaultCase() {
        Request.Body body = Instancio.create(Request.Body.class);
        Map<String, Collection<String>> headers = Instancio.ofMap(
                new TypeToken<String>() {
                },
                new TypeToken<Collection<String>>() {
                }).create();

        return Stream.of(
                Arguments.of("Exception calling Interac Payments Api, returned 418",
                        Response.builder()
                                .request(Request.create(POST, URL_SEND, headers, body, null))
                                .status(418)
                                .build(),
                        URL_SEND,
                        PATH_SEND)
        );
    }

    private static Stream<Arguments> internalError() {

        return buildStreamArguments(
                List.of(
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the INITIATE PAYMENT request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the STOP PAYMENT request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the SUBMIT PAYMENT request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the INITIATE CANCELLATION request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the INITIATE ROLLBACK request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the GET PAYMENT OPTIONS request",
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the REISSUE TRANSFER NOTIFICATION request"
                ),
                500);
    }

    private static Stream<Arguments> unavailable() {
        return buildStreamArguments(List.of(
                        "The Interac Payments API is not ready to handle the INITIATE PAYMENT request",
                        "The Interac Payments API is not ready to handle the STOP PAYMENT request",
                        "The Interac Payments API is not ready to handle the SUBMIT PAYMENT request",
                        "The Interac Payments API is not ready to handle the INITIATE CANCELLATION request",
                        "The Interac Payments API is not ready to handle the INITIATE ROLLBACK request",
                        "The Interac Payments API is not ready to handle the GET PAYMENT OPTIONS request"),
                503);
    }

    private static Stream<Arguments> ignorableExceptionHttpCodes() {
        return Stream.of(
                Arguments.of(401,
                        "The service wants to get a protected resource from the Interac Payments API without providing the proper " +
                                "authorization"),
                Arguments.of(429,
                        "The Interac Payments API blocked the GET PAYMENT OPTIONS request call due to rate limiting"),
                Arguments.of(500,
                        "The Interac Payments API encountered an unexpected condition that prevented it from fulfilling the GET PAYMENT " +
                                "OPTIONS request"),
                Arguments.of(403,
                        "The Interac Payments API refused to execute the GET PAYMENT OPTIONS request")
                );
    }

}
