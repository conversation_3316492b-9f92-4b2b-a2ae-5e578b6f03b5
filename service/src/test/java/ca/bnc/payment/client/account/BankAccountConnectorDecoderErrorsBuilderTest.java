package ca.bnc.payment.client.account;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.mapper.bankaccountconnector.BacErrorsMapper;
import ca.nbc.payment.etransfer.bankaccountconnector.model.ErrorModel;
import ca.nbc.payment.lib.service.logging.LoggingFacade;
import ch.qos.logback.classic.Logger;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.BDDMockito.willThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class BankAccountConnectorDecoderErrorsBuilderTest {

    private static final String ERROR_LOG = "An error occurred while parsing error response from the Bank Account Connector API";
    private static final OutgoingPaymentException.Error ERROR = Instancio.create(OutgoingPaymentException.Error.class);

    private final Logger logger = (Logger) LoggerFactory.getLogger(BankAccountConnectorDecoderErrorsBuilder.class.getName());

    private BankAccountConnectorDecoderErrorsBuilder errorBuilder;

    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private LoggingFacade loggingFacade;
    @Mock
    private BacErrorsMapper bacErrorsMapper;
    @Mock
    private Response response;
    @Mock
    private Response.Body body;
    @Mock
    private InputStream inputStream;

    @BeforeEach
    void setup() {
        errorBuilder = new BankAccountConnectorDecoderErrorsBuilder(objectMapper, loggingFacade, bacErrorsMapper);
        given(response.body()).willReturn(body);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(objectMapper, loggingFacade, bacErrorsMapper, response, body, inputStream);
    }

    @Test
    void givenStreamException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final IOException ioException = Instancio.create(IOException.class);
        given(body.asInputStream()).willThrow(ioException);

        OutgoingPaymentException.Error Errors = errorBuilder.buildErrorsFromResponseBody(response);

        assertThat(Errors).isNull();
        then(loggingFacade).should().error(logger, ERROR_LOG, ioException);
    }

    @Test
    void givenParsingException_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final RuntimeException exception = Instancio.create(RuntimeException.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willThrow(exception);

        OutgoingPaymentException.Error Errors = errorBuilder.buildErrorsFromResponseBody(response);

        assertThat(Errors).isNull();
        then(loggingFacade).should().error(logger, ERROR_LOG, exception);
        then(inputStream).should().close();
    }

    @Test
    void whenBuildErrorFromResponseBody_thenReturnErrors() throws IOException {
        final ErrorModel error = Instancio.create(ErrorModel.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willReturn(error);
        given(bacErrorsMapper.mapBankAccountConnectorErrorModelToError(error)).willReturn(ERROR);

        OutgoingPaymentException.Error actualErrors = errorBuilder.buildErrorsFromResponseBody(response);

        assertThat(actualErrors).isEqualTo(ERROR);
        then(inputStream).should().close();
    }

    @Test
    void givenParsingExceptionAndThrowable_whenBuildErrorFromResponseBody_thenLogError() throws IOException {
        final RuntimeException exception = mock(RuntimeException.class);
        final RuntimeException throwable = Instancio.create(RuntimeException.class);
        given(body.asInputStream()).willReturn(inputStream);
        given(objectMapper.readValue(inputStream, ErrorModel.class)).willThrow(exception);
        willThrow(throwable).given(inputStream).close();

        OutgoingPaymentException.Error Errors = errorBuilder.buildErrorsFromResponseBody(response);

        assertThat(Errors).isNull();
        then(loggingFacade).should().error(logger, ERROR_LOG, exception);
        then(inputStream).should().close();
        then(exception).should().addSuppressed(throwable);
    }
}