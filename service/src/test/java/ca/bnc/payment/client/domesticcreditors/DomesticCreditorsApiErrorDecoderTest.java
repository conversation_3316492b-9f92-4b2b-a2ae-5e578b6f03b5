package ca.bnc.payment.client.domesticcreditors;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.exception.domesticcreditors.DomesticCreditorsException;
import ca.bnc.payment.exception.domesticcreditors.RetryableDomesticCreditorsException;
import feign.Request;
import feign.Response;
import org.instancio.Instancio;
import org.instancio.TypeToken;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_BAD_REQUEST;
import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_GENERAL_EXCEPTION;
import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_INTERNAL_SERVER_ERROR;
import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_NOT_FOUND_CODE;
import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_NOT_FOUND_TEXT;
import static ca.bnc.payment.constant.DomesticCreditorsErrorMessage.DOMESTIC_CREDITORS_SERVICE_UNAVAILABLE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class DomesticCreditorsApiErrorDecoderTest {

    private DomesticCreditorsApiErrorDecoder decoder;

    @Mock
    private DomesticCreditorsErrorsBuilder errorBuilder;

    @BeforeEach
    void setup() {
        decoder = new DomesticCreditorsApiErrorDecoder(errorBuilder);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(errorBuilder);
    }

    @ParameterizedTest
    @MethodSource("responseCodes")
    void givenDomesticCreditorsException_whenDecode_thenThrowException(
            final Response response,
            final Exception expectedException,
            final OutgoingPaymentException.Error error
    ) {
        given(errorBuilder.buildErrorFromResponseBody(response)).willReturn(error);

        final Exception exception = decoder.decode(null, response);

        assertThat(exception)
                .usingRecursiveComparison()
                .isEqualTo(expectedException);
    }

    private static Stream<Arguments> responseCodes() {
        final Request request = Instancio.create(Request.class);
        final OutgoingPaymentException.Error error = Instancio.create(OutgoingPaymentException.Error.class);
        final OutgoingPaymentException.Error errorNotFound = new OutgoingPaymentException.Error(DOMESTIC_CREDITORS_NOT_FOUND_CODE, DOMESTIC_CREDITORS_NOT_FOUND_TEXT);

        return Stream.of(
                Arguments.of(Response.builder().request(request).status(400).build(),
                        new DomesticCreditorsException(DOMESTIC_CREDITORS_BAD_REQUEST, error),
                        error),
                Arguments.of(Response.builder().request(request).status(404).build(),
                        new DomesticCreditorsException(DOMESTIC_CREDITORS_NOT_FOUND_TEXT, errorNotFound),
                        errorNotFound),
                Arguments.of(Response.builder().request(request).status(418).build(),
                        new DomesticCreditorsException(String.format(DOMESTIC_CREDITORS_GENERAL_EXCEPTION, 418), error),
                        error),
                Arguments.of(Response.builder().request(request).status(500).build(),
                        new DomesticCreditorsException(DOMESTIC_CREDITORS_INTERNAL_SERVER_ERROR, error),
                        error),
                Arguments.of(Response.builder().request(request).status(503).build(),
                        new RetryableDomesticCreditorsException(DOMESTIC_CREDITORS_SERVICE_UNAVAILABLE),
                        null)
        );
    }
}
