package ca.bnc.payment.client.fraud;

import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class FraudApiOktaInterceptorTest {

    private static final String FRAUD_TOKEN_CONFIG = "fraudScope";
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_TOKEN = "Bearer jwtToken";

    @Mock
    private RequestInterceptor delegate;

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private OktaClientTokenManager oktaClientTokenManager;

    private FraudApiOktaInterceptor fraudApiInterceptor;

    @BeforeEach
    void setUp() {
        fraudApiInterceptor = new FraudApiOktaInterceptor(oktaClientTokenManager);
    }

    @AfterEach
    void noMoreInteractions() {
        verifyNoMoreInteractions(delegate, requestTemplate, oktaClientTokenManager);
    }

    @Test
    @DisplayName("should Create Correct Delegate When Using Okta Manager")
    void shouldCreateCorrectDelegateWhenUsingOktaManager() {
        given(oktaClientTokenManager.getAccessToken(FRAUD_TOKEN_CONFIG)).willReturn("jwtToken");

        fraudApiInterceptor.apply(requestTemplate);
        then(requestTemplate).should().header(eq(AUTHORIZATION_HEADER), eq(BEARER_TOKEN));
    }
  
}