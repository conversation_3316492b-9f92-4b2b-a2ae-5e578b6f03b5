package ca.bnc.payment.logging;

import ca.bnc.payment.exception.OutgoingPaymentException;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.Error;
import ca.bnc.payment.pmt_outgoing_payment_management_resources.generated.model.Errors;
import ch.qos.logback.classic.Logger;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.verifyNoMoreInteractions;

@ExtendWith(MockitoExtension.class)
class LoggerWrapperTest {

    private static final String ERROR_CODE = "TECHNICAL_ERROR";
    private static final String ERROR_MESSAGE = "error";
    private static final String ERROR_CODE2 = "TECHNICAL_ERROR2";
    private static final String ERROR_MESSAGE2 = "error2";
    private static final String RULE = "X03";
    private static final String PARTY_ORIGIN = "pmt-party-api";
    private static final String ERROR_MESSAGE_WITH_ONE_ERROR = "errors: [Error(code=TECHNICAL_ERROR, text=error)]";
    private static final String ERROR_MESSAGE_WITH_TWO_ERRORS =
            "errors: [Error(code=TECHNICAL_ERROR, text=error), Error(code=TECHNICAL_ERROR2, text=error2)]";

    @Mock
    private ca.nbc.payment.lib.service.logging.LoggingFacade loggingFacade;

    @Mock
    private Logger logger;

    private LoggerWrapper loggerWrapper;

    @BeforeEach
    void setUp() {
        loggerWrapper = new LoggerWrapper(loggingFacade);
    }

    @AfterEach
    void noMoreInteraction() {
        verifyNoMoreInteractions(
                loggingFacade,
                logger);
    }

    @Test
    void givenExceptionAndNoErrors_whenLogExceptionResponse_thenLog() {
        final OutgoingPaymentException exception = new OutgoingPaymentException(ERROR_MESSAGE);

        loggerWrapper.logExceptionResponse(logger, exception, null);

        then(loggingFacade).should().error(logger, ERROR_MESSAGE, exception);
    }

    @ParameterizedTest
    @MethodSource("exceptionData")
    void givenExceptionAndOneError_whenLogExceptionResponse_thenLog(final Exception exception) {
        final Errors errors = new Errors().errors(List.of(new Error(ERROR_CODE, ERROR_MESSAGE, PARTY_ORIGIN).rule(RULE)));

        loggerWrapper.logExceptionResponse(logger, exception, errors);

        if (exception instanceof OutgoingPaymentException outgoingPaymentException && outgoingPaymentException.hasErrors()) {
            then(loggingFacade).should().error(logger, ERROR_MESSAGE_WITH_ONE_ERROR);
        }
        then(loggingFacade).should().error(logger, ERROR_MESSAGE_WITH_ONE_ERROR, exception);
    }

    @ParameterizedTest
    @MethodSource("exceptionData")
    void givenExceptionAndTwoErrors_whenLogExceptionResponse_thenLog(final Exception exception) {
        final Errors errors = new Errors().errors(
                List.of(
                        new Error(ERROR_CODE, ERROR_MESSAGE, PARTY_ORIGIN).rule(RULE),
                        new Error(ERROR_CODE2, ERROR_MESSAGE2, PARTY_ORIGIN).rule(RULE)));

        loggerWrapper.logExceptionResponse(logger, exception, errors);

        if (exception instanceof OutgoingPaymentException outgoingPaymentException && outgoingPaymentException.hasErrors()) {
            then(loggingFacade).should().error(logger, ERROR_MESSAGE_WITH_ONE_ERROR);
        }
        then(loggingFacade).should().error(logger, ERROR_MESSAGE_WITH_TWO_ERRORS, exception);
    }

    private static Stream<Arguments> exceptionData() {
        final List<OutgoingPaymentException.Error> oneError = List.of(new OutgoingPaymentException.Error(ERROR_CODE, ERROR_MESSAGE));
        return Stream.of(
                Arguments.of(new OutgoingPaymentException(ERROR_MESSAGE, Collections.emptyList())),
                Arguments.of(new OutgoingPaymentException(ERROR_MESSAGE, oneError)),
                Arguments.of(new RuntimeException(ERROR_MESSAGE)));
    }

}
