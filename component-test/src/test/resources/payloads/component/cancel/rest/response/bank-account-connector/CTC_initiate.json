{"fi_to_fi_payment_status_report": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2019-05-05T17:29:12.000Z", "instructing_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "CA000612"}}}, "instructed_agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}}, "transaction_information_and_status": [{"original_group_information": {"original_message_identification": "d04273e9014645c2b12e3ef18ef8589c"}, "original_instruction_identification": "ec6e707c92024f42bb6be5ab33dedf2d", "original_end_to_end_identification": "d04273e9014645c2b12e3ef18ef811b", "original_transaction_identification": "d04273e9014645c2b12e3ef18ef811b", "transaction_status": "ACTC", "charges_information": {"amount": {"amount": 1709.66, "currency": "CAD"}, "agent": {"financial_institution_identification": {"clearing_system_member_identification": {"member_identification": "CA000612"}}}}, "acceptance_datetime": "2019-05-05T17:29:12.000Z", "effective_interbank_settlement_date": "2019-05-05T17:29:12.000Z", "clearing_system_reference": "ETR", "original_transaction_reference": {"amount": {"objectType": "instructed_amount", "instructed_amount": {"amount": 1709.66, "currency": "CAD"}}, "creditor_account": {"identification": {"objectType": "other", "other": {"identification": "612-56789-************", "scheme_name": {"objectType": "proprietary", "proprietary": "BANK_ACCT_NO"}, "issuer": ""}}}}}]}}