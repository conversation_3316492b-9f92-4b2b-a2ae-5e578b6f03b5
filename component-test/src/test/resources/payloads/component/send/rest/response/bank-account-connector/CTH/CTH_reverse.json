{"fi_to_fi_payment_status_report": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2023-09-06T14:45:22.000Z", "instructing_agent": {"financial_institution_identification": {"bicfi": "RN2UKHGZ", "clearing_system_member_identification": {"member_identification": "CA000612"}}}, "instructed_agent": {"financial_institution_identification": {"bicfi": "QTW7OTOA", "clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}}, "transaction_information_and_status": [{"original_group_information": {"original_message_identification": "d04273e9014645c2b12e3ef18ef8589c", "original_message_name_identification": "AGSFDHEWY"}, "original_instruction_identification": "ZZZZ9999", "original_end_to_end_identification": "BBBB9999", "original_transaction_identification": "BBBB9999", "transaction_status": "ACTC", "acceptance_datetime": "2023-09-06T14:45:56.923Z", "effective_interbank_settlement_date": "2023-09-06T14:45:56.923Z", "original_transaction_reference": {"amount": {"objectType": "instructed_amount", "instructed_amount": {"amount": 1709.66, "currency": "CAD"}}, "debtor_account": {"identification": {"objectType": "other", "other": {"identification": "222-19999-************", "scheme_name": {"objectType": "proprietary", "proprietary": "BANK_ACCT_NO"}}}}, "creditor": {"objectType": "party", "party": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "identification": {"objectType": "organisation_identification", "organisation_identification": {"other": [{"identification": "CCCC9999", "scheme_name": {"objectType": "code", "code": "BANK"}}]}}, "country_of_residence": "CA", "contact_details": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "mobile_number": "******-222-9999", "email_address": "<EMAIL>"}}}, "creditor_account": {"identification": {"objectType": "other", "other": {"identification": "356-47382-************", "scheme_name": {"objectType": "proprietary", "proprietary": "BANK_ACCT_NO"}}}}}}], "HTTP_header": {"id": "BBBB9999", "x-c1-client-id": "CA000612", "x-request-id": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "x-retry-indicator": "false"}}}