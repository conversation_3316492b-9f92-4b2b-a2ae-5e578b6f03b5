{"fi_to_fi_payment_status_report": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2023-09-06T14:45:22.000Z", "instructing_agent": {"financial_institution_identification": {"bicfi": "RN2UKHGZ", "clearing_system_member_identification": {"member_identification": "CA000612"}}}, "instructed_agent": {"financial_institution_identification": {"bicfi": "QTW7OTOA", "clearing_system_member_identification": {"member_identification": "NOTPROVIDED"}}}}, "transaction_information_and_status": [{"original_group_information": {"original_message_identification": "d04273e9014645c2b12e3ef18ef8589c", "original_message_name_identification": "AGSFDHEWY"}, "original_instruction_identification": "ZZZZ9999", "original_end_to_end_identification": "BBBB9999", "original_transaction_identification": "BBBB9999", "transaction_status": "ACTC", "acceptance_datetime": "2023-09-06T14:45:56.923Z", "effective_interbank_settlement_date": "2023-09-06T14:45:56.923Z", "clearing_system_reference": "7CF0CC577824BB54320B7F7FD3E87E4EF5A", "original_transaction_reference": {"amount": {"instructed_amount": {"amount": 1709.66, "currency": "CAD"}}, "debtor": {"party": {"identification": {"organisation_identification": {"other": [{"identification": "CCCC9999", "scheme_name": {"code": "BANK"}}]}}, "country_of_residence": "CA", "contact_details": {"mobile_number": "******-111-9999", "email_address": "<EMAIL>", "name": "<PERSON><PERSON>he Thepower"}, "name": "<PERSON><PERSON>he Thepower"}}, "debtor_account": {"identification": {"other": {"identification": "222-19999-************", "scheme_name": {"proprietary": "BANK_ACCT_NO"}}}}, "creditor": {"party": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "identification": {"organisation_identification": {"other": [{"identification": "CCCC9999", "scheme_name": {"code": "BANK"}}]}}, "country_of_residence": "CA", "contact_details": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "mobile_number": "******-222-9999", "email_address": "<EMAIL>"}}}, "creditor_account": {"identification": {"other": {"identification": "356-47382-************", "scheme_name": {"proprietary": "BANK_ACCT_NO"}}}}}}]}, "payment_transaction_token": "********", "fraud_check_result": {"score": 10, "reason": "<PERSON><PERSON> reason", "action": "ALLOW"}}