{"fiToFiCustomerDebitTransfer": {"groupHeader": {"messageIdentification": "d04273e9014645c2b12e3ef18ef8589c", "creationDateTime": "2023-09-06T14:45:22.001Z", "numberOfTransactions": "1", "settlementInformation": {"settlementMethod": "CLRG", "clearingSystem": {"proprietary": "ETR"}}, "instructingAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "CA000612"}}}, "instructedAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "NOTPROVIDED"}}}}, "directDebitTransactionInformation": [{"paymentIdentification": {"instructionIdentification": "ZZZZ9999", "endToEndIdentification": "BBBB1d99", "transactionIdentification": "BBBB1d99"}, "paymentTypeInformation": {"localInstrument": {"proprietary": "REGULAR_PAYMENT"}}, "interbankSettlementAmount": {"amount": 1109.66, "currency": "CAD"}, "interbankSettlementDate": "2023-08-01", "chargeBearer": "SLEV", "creditor": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "contactDetails": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "mobileNumber": "******-222-9999", "emailAddress": "<EMAIL>"}}, "creditorAccount": {"identification": {"other": {"identification": "111-19999-************", "schemeName": {"proprietary": "BANK_ACCT_NO"}}}}, "creditorAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "NOTPROVIDED"}}}, "debtor": {"name": "<PERSON><PERSON>he Thepower", "identification": {"organisationIdentification": {"other": [{"identification": "CCCC9999"}]}}, "contactDetails": {"name": "<PERSON><PERSON>he Thepower", "mobileNumber": "******-111-9999", "emailAddress": "<EMAIL>"}}, "debtorAccount": {"identification": {"other": {"identification": "222-19999-************", "schemeName": {"proprietary": "BANK_ACCT_NO"}}}}, "debtorAgent": {"financialInstitutionIdentification": {"clearingSystemMemberIdentification": {"memberIdentification": "NOTPROVIDED"}}}, "remittanceInformation": {"unstructured": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ytwewuesadfdah"]}, "directDebitTransaction": {"mandateRelatedInformation": {"mandateIdentification": "***********"}}}], "htTPHeader": {"id": "BBBB1d99", "xC1ClientId": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "xRequestId": "3ee01483-2b49-467c-8cde-c48d1a1fd1c0", "xRetryIndicator": "false"}}}