{"FIToFICustomerCreditTransfer": {"groupHeader": {"messageIdentification": "d04273e9014645c2b12e3ef18ef8589c", "creationDateTime": "2023-09-06T14:45:22.000Z", "numberOfTransactions": "1"}, "creditTransferTransactionInformation": {"paymentIdentification": {"instructionIdentification": "ZZZZ9999", "endToEndIdentification": "BBBB9999"}, "paymentTypeInformation": {"localInstrument": {"proprietary": "REGULAR_PAYMENT"}}, "interbankSettlementAmount": {"amount": 1709.66, "currency": "CAD"}, "interbankSettlementDate": "2023-09-06T14:45:56.923Z", "debtor": {"name": "<PERSON><PERSON>he Thepower", "identification": {"organisationIdentification": {"other": [{"identification": "CCCC9999"}]}}, "contactDetails": {"mobileNumber": "******-111-9999", "emailAddress": "<EMAIL>"}}, "debtorAccount": {"identification": {"other": {"identification": "222-19999-************"}}}, "creditor": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "contactDetails": {"mobileNumber": "******-222-9999", "emailAddress": "<EMAIL>"}}, "creditorAccount": {"identification": {"other": {"identification": "111-19999-************"}}}, "remittanceInformation": {"unstructured": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ytwewuesadfdah", "asdftgfhsadgadashdggsdfas"]}, "mandateRelatedInformation": {"mandateIdentification": "***********", "frequencyTypePeriod": "MNTH", "countPerPeriod": 17, "supplementaryData": {"numberOfRemaining": 0, "currentOccurrence": 1, "originalChannelType": "WEB", "originalChannelId": "5156"}}, "supplementaryData": {"creditorPreferredLanguage": "EN", "interacMoneyRequestId": "DDDD9999", "accountHolderName": "<PERSON><PERSON>he Thepower", "fraudSupplementaryInfo": {"clientIpAddress": "**************", "clientCardNumber": "****************", "clientDeviceFingerPrint": "ITM1234567899999", "clientAuthenticationMethod": "PASSWORD", "accountCreationDate": "2020-01-23"}, "paymentAuthentication": {"securityQuestion": "b8f53795fcda7ff8fbaf3c3cc30d27a264d1c08", "securityAnswer": "c8d1c9487641cfc213b918f91c0917ab41be5d8b6360b352b2c05c94200093ec", "hashType": "SHA2", "hashSalt": "*************"}, "clientType": "INDIVIDUAL"}}}}