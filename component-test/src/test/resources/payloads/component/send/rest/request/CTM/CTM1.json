{"FIToFICustomerCreditTransfer": {"groupHeader": {"messageIdentification": "d04273e9014645c2b12e3ef18ef8589c", "creationDateTime": "2019-05-05T17:29:12.123Z", "numberOfTransactions": "***********"}, "creditTransferTransactionInformation": {"paymentIdentification": {"instructionIdentification": "string"}, "paymentTypeInformation": {"localInstrument": {"proprietary": "REGULAR_PAYMENT"}}, "interbankSettlementAmount": {"amount": 44.44, "currency": "CAD"}, "interbankSettlementDate": "2020-01-23", "debtor": {"name": "string", "identification": {"organisationIdentification": {"other": [{"identification": "string"}]}}, "contactDetails": {"mobileNumber": "******-555-1212", "emailAddress": "<EMAIL>"}}, "debtorAccount": {"identification": {"other": {"identification": "111-22222-************"}}}, "creditor": {"name": "string", "contactDetails": {"mobileNumber": "******-555-1212", "emailAddress": "<EMAIL>"}}, "creditorAccount": {"identification": {"other": {"identification": "111-22222-************"}}}, "remittanceInformation": {"unstructured": ["string"]}, "mandateRelatedInformation": {"mandateIdentification": "string", "frequencyTypePeriod": "MNTH", "countPerPeriod": 17, "supplementaryData": {"numberOfRemaining": 0, "currentOccurrence": 1, "originalChannelType": "WEB", "originalChannelId": "OSFIN"}}, "supplementaryData": {"creditorPreferredLanguage": "EN", "interacMoneyRequestId": "string", "accountHolderName": "string", "creditorAutoDepositRegNumber": "string", "fraudSupplementaryInfo": {"clientIpAddress": "*******", "clientCardNumber": "****************", "clientDeviceFingerPrint": "ITM1234567890123", "clientAuthenticationMethod": "PASSWORD", "accountCreationDate": "2022-01-23"}, "paymentAuthentication": {"securityQuestion": "string", "securityAnswer": "string", "hashType": "SHA2", "hashSalt": "string"}, "clientType": "INDIVIDUAL"}}}}