{"version": "1.0.0", "entity": "DOMESTIC_ETRANSFER_PAYMENT", "eventType": "PAYMENT-TRANSACTION-DATA-EXPORTED", "eventTime": "2023-09-06T14:45:22.000Z", "instructionIdentification": "379CD1CD9A284A2A8F8A7B5A28499070", "endToEndBusinessIdentification": "BBBB9999", "channelId": "OSFIN", "channelType": "WEB", "clientId": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "clientType": "I", "paymentTypeCode": "QA", "msgDefIdr": "pacs.008.001.08", "rawData": {"instructionIdentification": "379CD1CD9A284A2A8F8A7B5A28499070", "endToEndBusinessIdentification": "BBBB9999", "approvalRequired": false, "messageDefinitionIdentifier": "pacs.008.001.08", "clientId": "30BD2E5746C6013778F4CD84D4533FAE6C2F452D0", "creationDatetime": "2023-09-06T14:45:22.000Z", "messageData": {"fiToFiCustomerCreditTransfer": {"group_header": {"message_identification": "d04273e9014645c2b12e3ef18ef8589c", "creation_datetime": "2023-09-06T14:45:22.000Z", "number_of_transactions": "1"}, "credit_transfer_transaction_information": [{"payment_identification": {"instruction_identification": "ZZZZ9999"}, "payment_type_information": {"local_instrument": {"proprietary": "REGULAR_PAYMENT"}}, "interbank_settlement_amount": {"amount": 1709.66, "currency": "CAD"}, "interbank_settlement_date": "2023-09-06", "debtor": {"name": "<PERSON><PERSON>he Thepower", "contact_details": {"mobile_number": "******-111-9999", "email_address": "<EMAIL>"}}, "debtor_account": {"identification": {"other": {"identification": "222-19999-************"}}}, "creditor": {"name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "contact_details": {"mobile_number": "******-222-9999", "email_address": "<EMAIL>"}}, "creditor_account": {"identification": {"other": {"identification": "111-19999-************"}}}, "remittance_information": {"unstructured": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ytwewuesadfdah", "asdftgfhsadgadashdggsdfas"]}}]}}, "supplementaryData": {"accountHolderName": "<PERSON><PERSON>he Thepower", "fraudSupplementaryInfo": {"clientIpAddress": "**************", "clientDeviceFingerPrint": "ITM1234567899999", "clientAuthenticationMethod": "PASSWORD"}, "interacMoneyRequestId": "DDDD9999", "interacUserId": "CA000612-user-240", "mandateRelatedInformation": {"mandateIdentification": "***********", "frequencyTypePeriod": "MNTH", "countPerPeriod": 17, "numberOfRemaining": 0, "currentOccurrence": 1}, "paymentAuthentication": {"securityQuestion": "b8f53795fcda7ff8fbaf3c3cc30d27a264d1c08", "securityAnswer": "c8d1c9487641cfc213b918f91c0917ab41be5d8b6360b352b2c05c94200093ec"}, "paymentDirection": "OUT", "statusReasonInformation": {"code": "TECHNICAL_ERROR", "proprietary": "AWS DynamoDB API is unreachable"}, "transactionStatus": "RJCT"}}}