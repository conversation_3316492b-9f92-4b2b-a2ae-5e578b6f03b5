Feature: Send payment - Unsuccessful Component Tests CTEx - Documentation: https://wiki.bnc.ca/x/b66meQ

  Scenario: CTE1 - Send payment called but interac payments API timeout
    Given a send payment request with body "CTE/CTE_BNC.json"
    And the send payment request header contains "CTE/CTE_BNC.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.752Z |
      | 2023-09-06T14:45:22.752Z |
    And <PERSON><PERSON> responds with status
      | 201 |
      | 201 |
      | 201 |
    And the party API will respond "200:CTE/CTE_party_response.json"
    And the domestic creditors API will respond "200:CTE/CTE.json" if called with creditor Id "028be1ec-b224-4273-981e-f55f059ab25a" when sending payment
    And the publication to PPTD is successful
    And the publication to Payment Notification is successful
    And the bank account connector API will respond "201:CTE/CTE.json" while "initiate debit" when sending payment
    And the "increment" limits velocity API will respond "204" if called with "CTE/CTE_increment_BNC.json" when sending payment
    And the interac API will respond "-1" while "initiate payment" when sending payment
    And the interac API will respond "200:CTE/CTE1-DELETE.json" while "stop payment" when sending payment
    And the callback API will respond "204" if called with "CTE/CTE1.json" when sending payment
    And commandId is generated
    And the "decrement" limits velocity API will respond "204" if called with "CTE/CTE_decrement.json" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the record "CTE/CTE_BNC.json" is present in the database after sending payment
    And the bank account connector API has been called as expected
    And the limits velocity API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after sending payment contains the text
      | CTE/CTE1_service_log.json |
      | CTE/CTE_callback_log.json |
    And verify the message "CTE/CTE1_BNC.json" is sent to PPTD kafka topic
    And verify the message "CTE/CTE1_payment_notification.json" is sent to Payment Notification kafka topic

  Scenario Outline: <CTE> - Send payment called but interac payments API <scenario>
    Given a send payment request with body "CTE/CTE_BNC.json"
    And the send payment request header contains "CTE/CTE_BNC.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.752Z |
      | 2023-09-06T14:45:22.752Z |
    And okta responds with status
      | 201 |
      | 201 |
      | 201 |
    And the party API will respond "200:CTE/CTE_party_response.json"
    And the domestic creditors API will respond "200:CTE/CTE.json" if called with creditor Id "028be1ec-b224-4273-981e-f55f059ab25a" when sending payment
    And the publication to PPTD is successful
    And the publication to Payment Notification is successful
    And the bank account connector API will respond "201:CTE/CTE.json" while "initiate debit" when sending payment
    And the "increment" limits velocity API will respond "204" if called with "CTE/CTE_increment_BNC.json" when sending payment
    And the interac API will respond "<interac_initiate_response>" while "initiate payment" when sending payment
    And the callback API will respond "204" if called with "CTE/<callback_request>" when sending payment
    And commandId is generated
    And the "decrement" limits velocity API will respond "204" if called with "CTE/CTE_decrement.json" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the record "CTE/CTE_BNC.json" is present in the database after sending payment
    And the bank account connector API has been called as expected
    And the limits velocity API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after sending payment contains the text
      | CTE/<log_msg>             |
      | CTE/CTE_callback_log.json |
    And verify the message "CTE/<pptd_message>" is sent to PPTD kafka topic
    And verify the message "CTE/<payment_notification_message>" is sent to Payment Notification kafka topic

    Examples:
      | CTE  | scenario    | interac_initiate_response                             | log_msg               | callback_request | pptd_message  | payment_notification_message   |
      | CTE2 | returns 400 | 400:CTE/CTE2.json                                     | CTE2_service_log.json | CTE2.json        | CTE2_BNC.json | CTE2_payment_notification.json |
      | CTE3 | returns 401 | 401                                                   | CTE3_service_log.json | CTE3.json        | CTE3_BNC.json | CTE3_payment_notification.json |
      | CTE4 | returns 403 | 403                                                   | CTE4_service_log.json | CTE4.json        | CTE4_BNC.json | CTE4_payment_notification.json |
      | CTE5 | returns 404 | 404:CTE/CTE5.json                                     | CTE5_service_log.json | CTE5.json        | CTE5_BNC.json | CTE5_payment_notification.json |
      | CTE6 | returns 429 | 429                                                   | CTE6_service_log.json | CTE6.json        | CTE6_BNC.json | CTE6_payment_notification.json |
      | CTE7 | returns 503 | 503:CTE/CTE7.json,503:CTE/CTE7.json,503:CTE/CTE7.json | CTE7_service_log.json | CTE7.json        | CTE7_BNC.json | CTE7_payment_notification.json |
      | CTE8 | returns 500 | 500                                                   | CTE8_service_log.json | CTE8.json        | CTE8_BNC.json | CTE8_payment_notification.json |