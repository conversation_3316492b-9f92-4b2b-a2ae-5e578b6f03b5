Feature: Send payment - Unsuccessful Component Tests CTJx - Documentation: https://wiki.bnc.ca/x/k66meQ

  Scenario: CTJ1 - Limits Velocity API does not reply
    Given a send payment request with body "CTJ/CTJ_BNC.json"
    And the send payment request header contains "CTJ/CTJ_BNC.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.000Z |
    And the publication to PPTD is successful
    And the publication to Payment Notification is successful
    And okta responds with scopes and statuses
      | scope                  | status |
      | partyScope             | 201    |
      | domesticCreditorsScope | 201    |
      | limitsVelocityScope    | 201    |
    And the party API will respond "200:CTJ/CTJ_party_response.json"
    And the domestic creditors API will respond "200:CTJ/CTJ.json" if called with creditor Id "028be1ec-b224-4273-981e-f55f059ab25a" when sending payment
    And the bank account connector API will respond "201:CTJ/CTJ_initiate.json" while "initiate debit" when sending payment
    And the "increment" limits velocity API will respond "-1" if called with "CTJ/CTJ_increment_BNC.json" when sending payment
    And commandId is generated
    And the "decrement" limits velocity API will respond "204" if called with "CTJ/CTJ_decrement.json" when sending payment
    And the callback API will respond "202" if called with "CTJ/CTJ1.json" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the okta call for scope "partyScope" has "succeeded"
    And the okta call for scope "domesticCreditorsScope" has "succeeded"
    And the okta call for scope "limitsVelocityScope" has "succeeded"
    And the record "CTJ/CTJ_error_BNC.json" is present in the database after sending payment
    And the fraud API has been called as expected
    And the bank account connector API has been called as expected
    And the limits velocity API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after sending payment contains the text
      | CTJ/CTJ1_log.json         |
      | CTJ/CTJ_callback_log.json |
    And verify the message "CTJ/CTJ1_BNC.json" is sent to PPTD kafka topic
    And verify the message "CTJ/CTJ1_payment_notification.json" is sent to Payment Notification kafka topic

  Scenario Outline: <CTJx> - Limits Velocity API returns a HTTP-<http_code> status code
    Given a send payment request with body "CTJ/CTJ_BNC.json"
    And the send payment request header contains "CTJ/CTJ_BNC.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.000Z |
    And the publication to PPTD is successful
    And the publication to Payment Notification is successful
    And okta responds with scopes and statuses
      | scope                  | status |
      | partyScope             | 201    |
      | domesticCreditorsScope | 201    |
      | limitsVelocityScope    | 201    |
    And the party API will respond "200:CTJ/CTJ_party_response.json"
    And the domestic creditors API will respond "200:CTE/CTE.json" if called with creditor Id "028be1ec-b224-4273-981e-f55f059ab25a" when sending payment
    And the bank account connector API will respond "201:CTJ/CTJ_initiate.json" while "initiate debit" when sending payment
    And the "increment" limits velocity API will respond "<limits_velocity_response>" if called with "CTJ/CTJ_increment_BNC.json" when sending payment
    And commandId is generated
    And the callback API will respond "202" if called with "CTJ/<callback_payload>" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the okta call for scope "partyScope" has "succeeded"
    And the okta call for scope "domesticCreditorsScope" has "succeeded"
    And the okta call for scope "limitsVelocityScope" has "succeeded"
    And the record "CTJ/CTJ_error_BNC.json" is present in the database after sending payment
    And the bank account connector API has been called as expected
    And the limits velocity API has been called as expected
    And the callback API has been called as expected
    And the log message after sending payment contains the text
      | CTJ/<log>                 |
      | CTJ/CTJ_callback_log.json |
    And verify the message "CTJ/<CTJx_pptd>.json" is sent to PPTD kafka topic
    And verify the message "CTJ/<CTJx_payment_notification>.json" is sent to Payment Notification kafka topic
    Examples:
      | CTJx_pptd | CTJx_payment_notification | limits_velocity_response | callback_payload | log           |
      | CTJ2_BNC  | CTJ2_payment_notification | 400:CTJ/CTJ2.json        | CTJ2.json        | CTJ2_log.json |
      | CTJ3_BNC  | CTJ3_payment_notification | 400:CTJ/CTJ3.json        | CTJ3.json        | CTJ3_log.json |
      | CTJ4_BNC  | CTJ4_payment_notification | 500:CTJ/CTJ4.json        | CTJ4.json        | CTJ4_log.json |

  Scenario Outline: CTJ5 - Okta replies with an error 400
    Given a send payment request with body "CTJ/CTJ_BNC.json"
    And the send payment request header contains "CTJ/CTJ_BNC.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.000Z |
    And the publication to PPTD is successful
    And the publication to Payment Notification is successful
    And okta responds with scopes and statuses
      | scope                  | status       |
      | partyScope             | 201          |
      | domesticCreditorsScope | 201          |
      | limitsVelocityScope    | <okta_error> |
    And the party API will respond "200:CTJ/CTJ_party_response.json"
    And the domestic creditors API will respond "200:CTJ/CTJ.json" if called with creditor Id "028be1ec-b224-4273-981e-f55f059ab25a" when sending payment
    And the bank account connector API will respond "201:CTJ/CTJ_initiate.json" while "initiate debit" when sending payment
    And commandId is generated
    And the callback API will respond "202" if called with "CTJ/CTJ5.json" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the okta call for scope "partyScope" has "succeeded"
    And the okta call for scope "domesticCreditorsScope" has "succeeded"
    And the okta call for scope "limitsVelocityScope" has "failed"
    And the callback API has been called as expected
    And verify the message "CTJ/CTJ5_BNC.json" is sent to PPTD kafka topic
    And verify the message "CTJ/CTJ5_payment_notification.json" is sent to Payment Notification kafka topic
    And the log message after sending payment contains the text
      | CTJ/CTJ5_service_log.json                   |
      | CTJ/CTJ_callback_log.json                   |
      | CTJ/CTJ_pptd_payload_log_bnc.json           |
      | CTJ/CTJ_kafka_pptd_log.json                 |
      | CTJ/CTJ_notification_payload_log.json       |
      | CTJ/CTJ_kafka_payment_notification_log.json |
    Examples:
      | okta_error |
      | 400        |
      | 401        |