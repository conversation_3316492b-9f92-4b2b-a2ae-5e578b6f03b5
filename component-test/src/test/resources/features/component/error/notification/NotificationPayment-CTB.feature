Feature: Reissue notification - Unsuccessful Component Tests CTBx - Documentation: https://wiki.bnc.ca/x/Ha_meQ

  Scenario Outline: <CTB> - Notification Payment failed with HTTP-<error> error for <scenario>
    Given the record "CTB_notification.json" is added in the database for notification
    And a notification payment request with body "CTB/CTB.json"
    And the notification payment request header contains "CTB/CTB.json"
    And the interac API will respond "<interac_response>" when reissuing notification
    When the notification payment request is processed
    Then a <response_status> status is received after notification payment with body "<response_body>"
    And the log message after notification payment contains the text
      | <service_logs> |
    Examples:
      | CTB  | error | interac_response | response_status | response_body | service_logs              | scenario                                                   |
      | CTB1 | 500   | -1               | 500             | CTB/CTB1.json | CTB/CTB1_service_log.json | The interac payment Api timed out                          |
      | CTB4 | 500   | 401              | 500             | CTB/CTB4.json | CTB/CTB4_service_log.json | The interac payment Api wants to get a protected ressource |
      | CTB5 | 500   | 403              | 500             | CTB/CTB5.json | CTB/CTB5_service_log.json | The interac payment Api refused to execute the operation   |
      | CTB7 | 500   | 429              | 500             | CTB/CTB7.json | CTB/CTB7_service_log.json | The interac payment Api is blocked due to rate limiting    |
      | CTB8 | 500   | 500              | 500             | CTB/CTB8.json | CTB/CTB8_service_log.json | The interac payment Api encountered an unexpected error    |

  Scenario Outline: <CTB> - Notification Payment failed with HTTP-<error> error for <scenario> with interac error body
    Given the record "CTB_notification.json" is added in the database for notification
    And a notification payment request with body "CTB/CTB.json"
    And the notification payment request header contains "CTB/CTB.json"
    And the interac API will respond "<interac_response>" when reissuing notification
    When the notification payment request is processed
    Then a <response_status> status is received after notification payment with body "<response_body>"
    And the log message after notification payment contains the text
      | <interac_logs> |
      | <service_logs> |
    Examples:
      | CTB  | error | interac_response                          | response_status | response_body | interac_logs              | service_logs              | scenario                                                       |
      | CTB2 | 400   | 400:CTB2.json                             | 500             | CTB/CTB2.json | CTB/CTB2_interac_log.json | CTB/CTB2_service_log.json | The interac payment Api received a bad request                 |
      | CTB3 | 400   | 400:CTB3.json                             | 400             | CTB/CTB3.json | CTB/CTB3_interac_log.json | CTB/CTB3_service_log.json | The interac payment Api received a bad request                 |
      | CTB9 | 503   | 503:CTB9.json,503:CTB9.json,503:CTB9.json | 500             | CTB/CTB9.json | CTB/CTB9_interac_log.json | CTB/CTB9_service_log.json | The interac payment Api is not ready to handle the transaction |

  Scenario: CTB6 - Notification Payment failed with HTTP-404 error because Interac didn't find the ressource
    Given the record "CTB_notification.json" is added in the database for notification
    And a notification payment request with body "CTB/CTB.json"
    And the notification payment request header contains "CTB/CTB.json"
    And the interac API will respond "404:CTB6.json" when reissuing notification
    When the notification payment request is processed
    Then a 404 status is received
    And the log message after notification payment contains the text
      | CTB/CTB6_interac_log.json |
      | CTB/CTB6_service_log.json |