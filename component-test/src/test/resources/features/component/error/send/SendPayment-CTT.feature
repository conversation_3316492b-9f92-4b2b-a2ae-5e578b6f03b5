Feature: Send payment - Unsuccessful Component Tests CTTx - Documentation: https://wiki.bnc.ca/x/fq6meQ

  Scenario Outline: <CTT> - Initiate interac payment but AWS DynamoDB have <scenario> error during interac id update
    Given a send payment request with body "CTT/CTT.json"
    And the send payment request header contains "CTT/CTT.json"
    And the system dates are set to
      | 2023-09-06T14:45:21.398Z |
      | 2023-09-06T14:45:22.000Z |
      | 2023-09-06T14:45:23.752Z |
    And the publication to PPTD is successful
    And the bank account connector API will respond "201:CTT/CTT.json" while "initiate debit" when sending payment
    And the database throws an error <db_status> on update item number 2
    And the party API will respond "200:Valid_party_response.json"
    And the interac API will respond "201:CTT/CTT.json" while "initiate payment" when sending payment
    And the interac API will respond "200" while "stop payment" when sending payment
    And the callback API will respond "204" if called with "CTT/CTT.json" when sending payment
    When the send payment request is processed
    Then a 202 status is received
    And the record "CTT/CTT.json" is present in the database after sending payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after sending payment contains the text
      | CTT/<log_msg>             |
      | CTT/CTT_callback_log.json |
    And verify the message "CTT/<CTT>.json" is sent to PPTD kafka topic
    And verify there is no message sent to Payment Notification topic
    Examples:
      | CTT  | scenario           | db_status | log_msg                |
      | CTT1 | timeout dynamodb   | -1        | CTT1_dynamodb_log.json |
      | CTT2 | error 400 dynamodb | 400       | CTT2_dynamodb_log.json |
      | CTT3 | error 500 dynamodb | 500       | CTT3_dynamodb_log.json |
