Feature: Cancel payment - Unsuccessful Component Tests CTYx - Documentation: https://wiki.bnc.ca/x/7q6meQ

  Scenario Outline: <CTY> - Cancel Payment - Cancellation failed because <scenario>
    Given the record "CTY/CTY_before_cancel.json" is added in the database
    And a cancel payment request with body "CTY/CTY.json"
    And the cancel payment request header contains "CTY/CTY.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.000Z |
    And the bank account connector API will respond "500" while "initiate credit" when canceling payment
    And the database throws an error <databaseStatus> on update item number 2
    And the callback API will respond "204" if called with "CTY/CTY.json" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the record "CTY/CTY_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTY/CTY_bank_account_connector_log_error.json |
      | CTY/<service_logs>                            |
      | CTY/CTY_callback_log.json                      |
    Examples:
      | CTY  | databaseStatus | service_logs          | scenario                                     |
      | CTY1 | -1             | CTY1_service_log.json | AWS DynamoDb did not reply                   |
      | CTY2 | 400            | CTY2_service_log.json | AWS DynamoDb throws a bad request            |
      | CTY3 | 500            | CTY3_service_log.json | AWS DynamoDb encountered an unexpected error |