Feature: Cancel payment - Unsuccessful Component Tests CTCx - Documentation: https://wiki.bnc.ca/x/266meQ

  Scenario: <CTD1>:  - Cancel Payment failed because bank account connector is unreachable
    Given the record "CTD/CTD_before_cancel.json" is added in the database
    And a cancel payment request with body "CTD/CTD.json"
    And the cancel payment request header contains "CTD/CTD.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "201:CTD_initiate.json" while "initiate cancel payment" when canceling payment
    And the bank account connector API will respond "-1" while "confirm credit" when canceling payment
    And the bank account connector API will respond "201" while "reverse credit" when canceling payment
    And the interac API will respond "204" while "rollback cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTD/CTD1.json" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the database is called 2 times for "UPDATE"
    And the record "CTD/CTD_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTD/CTD1_service_log.json |
      | CTD/CTD_callback_log.json  |

  Scenario Outline: <CTD>:  - Cancel Payment failed because <scenario>
    Given the record "CTD/CTD_before_cancel.json" is added in the database
    And a cancel payment request with body "CTD/CTD.json"
    And the cancel payment request header contains "CTD/CTD.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "201:CTD_initiate.json" while "initiate cancel payment" when canceling payment
    And the bank account connector API will respond "<bankAccountResponse>" while "confirm credit" when canceling payment
    And the interac API will respond "204" while "rollback cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTD/<callback_body>" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the database is called 2 times for "UPDATE"
    And the record "CTD/CTD_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTD/<service_logs>        |
      | CTD/CTD_callback_log.json |
    Examples:
      | CTD  | bankAccountResponse                       | service_logs          | callback_body | scenario                                                                                                      |
      | CTD2 | 400:CTD2.json                             | CTD2_service_log.json | CTD2.json     | bank account connector received a request associated to a non active account                                  |
      | CTD7 | 500                                       | CTD7_service_log.json | CTD7.json     | bank account connector is not ready to handle the request                                                     |
      | CTD8 | 503:CTD8.json,503:CTD8.json,503:CTD8.json | CTD8_service_log.json | CTD8.json     | bank account connector did encountered an unexpected condition that prevented it from fulfilling the request. |
