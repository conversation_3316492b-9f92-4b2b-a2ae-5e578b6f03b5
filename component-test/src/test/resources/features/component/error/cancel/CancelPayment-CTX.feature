Feature: Cancel payment - Unsuccessful Component Tests CTXx - Documentation: https://wiki.bnc.ca/x/6K6meQ

  Scenario: <CTX> - Cancel Payment - Interac Rollback failed because interac api is unreachable
    Given the record "CTX/CTX_before_cancel.json" is added in the database
    And a cancel payment request with body "CTX/CTX.json"
    And the cancel payment request header contains "CTX/CTX.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.123000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "-1" while "initiate cancel payment" when canceling payment
    And the interac API will respond "-1" while "rollback cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTX/CTX.json" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the record "CTX/CTX_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTX/CTX_service_log.json              |
      | CTX/CTX1_service_log_second_call.json |
      | CTX/CTX_callback_log.json             |

  Scenario Outline: <CTX> - Cancel Payment - Interac Rollback failed because <scenario>
    Given the record "CTX/CTX_before_cancel.json" is added in the database
    And a cancel payment request with body "CTX/CTX.json"
    And the cancel payment request header contains "CTX/CTX.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.123000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "-1" while "initiate cancel payment" when canceling payment
    And the interac API will respond "<interac_initiate_response>" while "rollback cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTX/CTX.json" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the record "CTX/CTX_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTX/CTX_service_log.json  |
      | <service_logs>            |
      | CTX/CTX_callback_log.json |
    Examples:
      | CTX  | interac_initiate_response                 | service_logs                          | scenario                                                                                               |
      | CTX2 | 400:CTX2.json                             | CTX/CTX2_service_log_second_call.json | The interac Api received a bad request                                                                 |
      | CTX3 | 401                                       | CTX/CTX3_service_log_second_call.json | The interac Api trying to access a ressource withoud proper authorization                              |
      | CTX4 | 403                                       | CTX/CTX4_service_log_second_call.json | The interac Api refused to execute the request                                                         |
      | CTX5 | 404:CTX5.json                             | CTX/CTX5_service_log_second_call.json | The interac Api transfer does not exist                                                         |
      | CTX6 | 429                                       | CTX/CTX6_service_log_second_call.json | The interac Api blocked the request due to rate limiting                                               |
      | CTX7 | 503:CTX7.json,503:CTX7.json,503:CTX7.json | CTX/CTX7_service_log_second_call.json | The interac Api did encountered an unexpected condition that prevented it from fulfilling the request. |
      | CTX8 | 500                                       | CTX/CTX8_service_log_second_call.json | The Interac Payments API is not ready to handle the request                                            |
