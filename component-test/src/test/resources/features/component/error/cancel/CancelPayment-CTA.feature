Feature: Cancel payment - Unsuccessful Component Tests CTAx - Documentation: https://wiki.bnc.ca/x/0q6meQ

  Scenario: CTA1 - Cancel Payment failed with HTTP 404 error due to Payment Not found in database
    Given a cancel payment request with body "CTA/CTA1.json"
    And the cancel payment request header contains "CTA/CTA1.json"
    When the cancel payment request is processed
    Then a 404 status is received
    And the database is called 1 times for "GET"
    And the log message after canceling payment contains the text
      | CTA/CTA1_service_log.json |

  Scenario: CTA2 - Cancel Payment failed with HTTP 400 error due to Payment Not found in database
    Given the record "CTA/CTA2.json" is added in the database
    And a cancel payment request with body "CTA/CTA2.json"
    And the cancel payment request header contains "CTA/CTA2.json"
    When the cancel payment request is processed
    Then a 400 status is received after canceling payment with body "CTA/CTA2.json" and message contains "Payment is not in a valid status"
    And the database is called 1 times for "GET"
    And the log message after canceling payment contains the text
      | CTA/CTA2_service_log.json |

  Scenario: CTA3 - Cancel Payment failed with HTTP 500 error because AWS DynamoDB API does not reply
    Given a cancel payment request with body "CTA/CTA3.json"
    And the cancel payment request header contains "CTA/CTA3.json"
    And the database does not respond within the timeout period on "GET"
    When the cancel payment request is processed
    Then a 500 status is received after canceling payment with body "CTA/CTA3.json"
    And the log message after canceling payment contains the text
      | CTA/CTA3_service_log.json |

  Scenario: CTA4 - Cancel Payment failed with HTTP 500 error because AWS DynamoDB API encountered an unexpected error
    Given a cancel payment request with body "CTA/CTA4.json"
    And the cancel payment request header contains "CTA/CTA4.json"
    And the database throws an error 500 when "GET"
    When the cancel payment request is processed
    Then a 500 status is received after canceling payment with body "CTA/CTA4.json"
    And the log message after canceling payment contains the text
      | CTA/CTA4_service_log.json |

  Scenario: CTA6 - Cancel Payment failed with HTTP 400 error due to Payment already canceled
    Given the record "CTA/CTA6.json" is added in the database
    And a cancel payment request with body "CTA/CTA6.json"
    And the cancel payment request header contains "CTA/CTA6.json"
    When the cancel payment request is processed
    Then a 400 status is received after canceling payment with body "CTA/CTA6.json" and message contains "Payment is already canceled"
    And the database is called 1 times for "GET"
    And the log message after canceling payment contains the text
      | CTA/CTA6_service_log.json |
