Feature: Cancel payment - Unsuccessful Component Tests CTEx - Documentation: https://wiki.bnc.ca/x/3q6meQ

  Scenario Outline: <CTE>:  - Cancel Payment failed because <scenario>
    Given the record "CTE/CTE_before_cancel.json" is added in the database
    And a cancel payment request with body "CTE/CTE.json"
    And the cancel payment request header contains "CTE/CTE.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "201:CTG1_initiate.json" while "initiate cancel payment" when canceling payment
    And the bank account connector API will respond "200" while "confirm credit" when canceling payment
    And the interac API will respond "<interacResponse>" while "commit cancel payment" when canceling payment
    And the bank account connector API will respond "201" while "reverse credit" when canceling payment
    And the interac API will respond "204" while "rollback cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTE/<callback_body>" when canceling payment
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the database is called 2 times for "UPDATE"
    And the record "CTE/CTE_after_cancel.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTE/<service_logs>        |
      | CTE/CTE_callback_log.json |
    Examples:
      | CTE  | interacResponse                           | service_logs          | callback_body | scenario                                                                                                                         |
      | CTE2 | -1                                        | CTE2_service_log.json | CTE2.json     | interac api commit domestic cancel payment did not respond                                                                       |
      | CTE3 | 400:CTE3.json                             | CTE3_service_log.json | CTE3.json     | interac api commit domestic cancel payment received a bad request                                                                |
      | CTE4 | 401                                       | CTE4_service_log.json | CTE4.json     | interac api commit domestic cancel payment is trying to access a ressource without proper authorization                          |
      | CTE5 | 403                                       | CTE5_service_log.json | CTE5.json     | interac api commit domestic cancel payment refused to execute the request                                                        |
      | CTE6 | 404:CTE6.json                             | CTE6_service_log.json | CTE6.json     | interac api commit domestic cancel payment could not find the transaction                                                        |
      | CTE7 | 429                                       | CTE7_service_log.json | CTE7.json     | interac api commit domestic cancel payment blocked the request call due to rate limiting                                         |
      | CTE8 | 500                                       | CTE8_service_log.json | CTE8.json     | interac api commit domestic cancel payment is not ready to handle the request                                                    |
      | CTE9 | 503:CTE9.json,503:CTE9.json,503:CTE9.json | CTE9_service_log.json | CTE9.json     | interac api commit domestic cancel payment did encountered an unexpected condition that prevented it from fulfilling the request |

  Scenario: <CTE11>:  - Cancel Payment commit success but failed update database
    Given the record "CTE/CTE_before_cancel.json" is added in the database
    And a cancel payment request with body "CTE/CTE.json"
    And the cancel payment request header contains "CTE/CTE.json"
    And the system dates are set to
      | 2019-05-05T17:29:12.000Z |
    And the bank account connector API will respond "201" while "initiate credit" when canceling payment
    And the interac API will respond "201:CTG1_initiate.json" while "initiate cancel payment" when canceling payment
    And the bank account connector API will respond "200" while "confirm credit" when canceling payment
    And the interac API will respond "204" while "commit cancel payment" when canceling payment
    And the callback API will respond "204" if called with "CTE/CTE10.json" when canceling payment
    And the database throws an error 500 on update item number 2
    When the cancel payment request is processed
    Then a 202 status is received
    And the database is called 1 times for "GET"
    And the database is called 2 times for "UPDATE"
    And the record "CTE/CTE11.json" is present in the database after canceling payment
    And the bank account connector API has been called as expected
    And the interac API has been called as expected
    And the callback API has been called as expected
    And the log message after canceling payment contains the text
      | CTE/CTE11_service_log.json  |
      | CTE/CTE_success_callback_log.json |