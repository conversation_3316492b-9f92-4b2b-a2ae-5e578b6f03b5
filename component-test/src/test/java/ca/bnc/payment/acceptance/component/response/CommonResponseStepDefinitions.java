package ca.bnc.payment.acceptance.component.response;

import ca.bnc.payment.acceptance.commons.ComponentTestContext;
import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import ca.bnc.payment.acceptance.commons.utils.LoggingEventsUtil;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import io.cucumber.java.en.Then;

public class CommonResponseStepDefinitions extends AbstractResponseCommonMethods {

    public CommonResponseStepDefinitions(
            final ComponentTestContext componentTestContext,
            final FileUtils fileUtils,
            final LoggingEventsUtil loggingEventsUtil,
            final ListAppender<ILoggingEvent> loggingEventListAppender) {

        super(componentTestContext, fileUtils, loggingEventsUtil, loggingEventListAppender);
    }

    @Then("a {int} status is received")
    public void aStatusIsReceived(final int statusCode) throws Exception {
        validateResponse(statusCode, null);
    }
}