package ca.bnc.payment.acceptance.component.providers.limitsvelocity;

import ca.bnc.payment.acceptance.commons.ComponentTestContext;
import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import ca.bnc.payment.util.IdGenerator;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import org.mockserver.client.MockServerClient;
import org.mockserver.matchers.MatchType;
import org.mockserver.model.HttpRequest;

import static org.mockito.Mockito.when;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.JsonBody.json;

public class LimitsVelocityStepDefinitions extends AbstractLimitsVelocityCommonMethods {

    private static final String BASE_REQUEST_BODY_PATH = "payloads/component/send/rest/request/limits-velocity/";
    private static final String BASE_RESPONSE_BODY_PATH = "payloads/component/send/rest/response/limits-velocity/";

    private static final String COMMAND_ID = "c48c372b265b4aeab0fe228a49db8ffb";

    private final IdGenerator generator;

    public LimitsVelocityStepDefinitions(
            final ComponentTestContext componentTestContext,
            final MockServerClient mockServerClient,
            final FileUtils fileUtils,
            final IdGenerator generator) {
        super(componentTestContext, mockServerClient, fileUtils);
        this.generator = generator;
    }

    @Given("the {string} limits velocity API will respond {string} if called with {string} when sending payment")
    public void theLimitsVelocityApiWillRespondWhenSendingPayment(
            final String incrementOrDecrementEndpoint,
            final String responsesParametersList,
            final String requestPayloadPath) {
        final HttpRequest request = buildLimitsVelocityRequest(BASE_REQUEST_BODY_PATH + requestPayloadPath, incrementOrDecrementEndpoint);
        mockLimitsVelocityCalls(responsesParametersList, BASE_RESPONSE_BODY_PATH, request);
    }

    @Given("commandId is generated")
    public void commandIdIsGenerated() {
        when(generator.generateCommandId()).thenReturn(COMMAND_ID);
    }

    @Then("the limits velocity API has been called as expected")
    public void theLimitsVelocityApiHasBeenCalledAsExpected() {
        verifyProviderRequests(PROVIDER_LIMITS_VELOCITY);
    }

    protected HttpRequest buildLimitsVelocityRequest(final String path, final String endpoint) {
        final String bodyAsString = fileUtils.getContentString(path);
        final String channelId = componentTestContext.getHttpHeaders().getFirst(X_CHANNEL_ID);
        return request()
                .withMethod("POST")
                .withHeader("x-channel-id", channelId)
                .withHeader("x-request-id", "3ee01483-2b49-467c-8cde-c48d1a1fd1c0")
                .withHeader("Accept-version", "v1")
                .withBody(json(bodyAsString, MatchType.STRICT))
                .withPath("/velocities/" + endpoint);
    }

}
