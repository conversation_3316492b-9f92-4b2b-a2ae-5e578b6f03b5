package ca.bnc.payment.acceptance.component.database;

import ca.bnc.payment.acceptance.commons.ComponentTestContext;
import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import ca.bnc.payment.model.repository.OutgoingDomesticPayment;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBRequestHandler;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;

import java.io.IOException;

import static org.mockito.Mockito.reset;

public class SendDatabaseStepDefinitions extends AbstractDatabaseCommonMethods {

    private static final String DATA_DIRECTORY_DYNAMODB_PATH = "payloads/component/send/dynamodb/";

    public SendDatabaseStepDefinitions(final DynamoDbTable<OutgoingDomesticPayment> outgoingDomesticPaymentTable,
                                       final LocalDynamoDBRequestHandler mockRequestHandler,
                                       final FileUtils fileUtils,
                                       final ComponentTestContext componentTestContext) {

        super(outgoingDomesticPaymentTable, mockRequestHandler, fileUtils, componentTestContext);
    }

    @Before
    public void setup() {
        createTable();
    }

    @Given("the record {string} is already present in the database")
    public void theRecordIsAlreadyPresentInTheDatabase(final String filename) throws IOException {
        final String filePath = DATA_DIRECTORY_DYNAMODB_PATH + filename;
        final OutgoingDomesticPayment outgoingDomesticPayment = fileUtils.getContentObject(filePath, OutgoingDomesticPayment.class);
        outgoingDomesticPaymentTable.putItem(outgoingDomesticPayment);
        reset(mockRequestHandler);
    }

    @Then("the record {string} is present in the database after sending payment")
    public void theRecordIsPresentInTheDatabaseAfterSendingPayment(final String filename) throws IOException {
        assertThatRecordIsPresentInDatabase(DATA_DIRECTORY_DYNAMODB_PATH + filename);
    }

}
