package ca.bnc.payment.acceptance.component.providers;

import ca.bnc.payment.acceptance.commons.ComponentTestContext;
import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import io.cucumber.java.Before;
import org.mockserver.client.MockServerClient;

public class ProvidersCommonStepDefinitions extends AbstractProvidersCommonMethods {

    public ProvidersCommonStepDefinitions(
            final ComponentTestContext componentTestContext,
            final MockServerClient mockServerClient,
            final FileUtils fileUtils) {

        super(componentTestContext, mockServerClient, fileUtils);
    }

    @Before
    public void setup() {
        resetMockServerClient();
    }
}