package ca.bnc.payment.acceptance.commons;

import lombok.Getter;
import lombok.Setter;
import org.mockserver.model.HttpRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.web.servlet.ResultActions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class ComponentTestContext {

    private String requestBodyAsString;
    private String end2endIdentification;
    private String etransferId;
    private HttpHeaders httpHeaders;
    private ResultActions response;
    private Map<String, List<HttpRequest>> providersRequests = new HashMap<>();

    public void resetFields() {
        this.requestBodyAsString = null;
        this.end2endIdentification = null;
        this.etransferId = null;
        this.httpHeaders = null;
        this.response = null;
        this.providersRequests.clear();
    }
}
