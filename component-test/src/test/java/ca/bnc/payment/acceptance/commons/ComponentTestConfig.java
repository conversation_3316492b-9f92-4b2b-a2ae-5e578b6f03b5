package ca.bnc.payment.acceptance.commons;

import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import ca.bnc.payment.acceptance.commons.utils.LoggerListAppenderUtil;
import ca.bnc.payment.acceptance.commons.utils.LoggingEventsUtil;
import ca.bnc.payment.mapper.fraud.FraudApiIdGenerator;
import ca.bnc.payment.util.IdGenerator;
import ca.nbc.payment.pmt_security_library.okta.OktaClientTokenManager;
import ca.nbc.payment.pmt_security_library.okta.config.SpecializeJwks;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.amazonaws.services.dynamodbv2.local.main.ServerRunner;
import com.amazonaws.services.dynamodbv2.local.server.AbstractLocalDynamoDBServerHandler;
import com.amazonaws.services.dynamodbv2.local.server.DynamoDBProxyServer;
import com.amazonaws.services.dynamodbv2.local.server.LocalDynamoDBRequestHandler;
import org.mockito.AdditionalAnswers;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Clock;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.mockserver.configuration.Configuration.configuration;

@TestConfiguration
public class ComponentTestConfig {

    @Bean(destroyMethod = "stop")
    public MockServerClient mockServerClient() {
        return ClientAndServer.startClientAndServer(configuration().disableLogging(true), 8081);
    }

    @Bean(destroyMethod = "stop")
    public DynamoDBProxyServer dynamoDBLocal() throws Exception {
        System.setProperty("sqlite4java.library.path", "native-libs");
        final DynamoDBProxyServer server = ServerRunner.createServerFromCommandLineArgs(
                new String[]{"-inMemory", "-port", "8001"});
        server.start();
        return server;
    }

    @Bean
    public LocalDynamoDBRequestHandler mockRequestHandler(final DynamoDBProxyServer server) {
        final AbstractLocalDynamoDBServerHandler serverHandler = (AbstractLocalDynamoDBServerHandler)
                ReflectionTestUtils.getField(server, "serverHandler");
        assert serverHandler != null;
        final LocalDynamoDBRequestHandler reqHandler = (LocalDynamoDBRequestHandler)
                ReflectionTestUtils.getField(serverHandler, "primaryHandler");
        assert reqHandler != null;
        final LocalDynamoDBRequestHandler mockRequestHandler = spy(reqHandler);

        ReflectionTestUtils.setField(serverHandler, "primaryHandler", mockRequestHandler);

        return mockRequestHandler;
    }

    @Bean
    @Primary
    public Clock mockClock() {
        return mock(Clock.class, AdditionalAnswers.delegatesTo(Clock.systemUTC()));
    }

    @SpyBean    // Although deprecated, this works as we want, but @MockitoSpyBean does not. See https://github.com/spring-projects/spring-framework/issues/33934
    public ThreadPoolTaskExecutor workerThreadExecutor;

    @Bean
    public FileUtils fileUtils() {
        return new FileUtils();
    }

    @Bean
    public LoggingEventsUtil loggingEventsUtil() {
        return new LoggingEventsUtil();
    }

    @Bean
    public ListAppender<ILoggingEvent> loggingEventListAppender() {
        return LoggerListAppenderUtil.loggingEventListAppender;
    }

    @Bean
    ComponentTestContext componentTestContext() {
        return new ComponentTestContext();
    }

    @Primary
    @Bean
    public FraudApiIdGenerator fraudTraceIdGenerator() {
        return mock(FraudApiIdGenerator.class);
    }

    @Primary
    @Bean
    @SuppressWarnings("unchecked")
    public KafkaTemplate<String, Object> kafkaTemplateMock() {
        return mock(KafkaTemplate.class);
    }

    @Primary
    @Bean
    public IdGenerator mockIdGenerator() {
        return mock(IdGenerator.class);
    }

    @Bean
    @Primary
    public OktaClientTokenManager oktaClientTokenManagerMock() {
        SpecializeJwks specializeJwks = mock(SpecializeJwks.class);
        when(specializeJwks.isEnabled()).thenReturn(true);
        OktaClientTokenManager oktaClientTokenManager = mock(OktaClientTokenManager.class);
        when(oktaClientTokenManager.specializeJwks(any())).thenReturn(specializeJwks);
        return oktaClientTokenManager;
    }
}
