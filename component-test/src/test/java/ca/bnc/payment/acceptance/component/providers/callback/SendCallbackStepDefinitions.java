package ca.bnc.payment.acceptance.component.providers.callback;

import ca.bnc.payment.acceptance.commons.ComponentTestContext;
import ca.bnc.payment.acceptance.commons.utils.FileUtils;
import io.cucumber.java.en.Given;
import org.mockserver.client.MockServerClient;
import org.mockserver.model.HttpRequest;

public class SendCallbackStepDefinitions extends AbstractCallbackCommonMethods {

    private static final String BASE_REQUEST_BODY_PATH = "payloads/component/send/rest/request/callback/";

    public SendCallbackStepDefinitions(
            final ComponentTestContext componentTestContext,
            final MockServerClient mockServerClient,
            final FileUtils fileUtils) {

        super(componentTestContext, mockServerClient, fileUtils);
    }

    @Given("the callback API will respond {string} if called with {string} when sending payment")
    public void theCallbackApiWillRespondWhenSendingPayment(final String responsesParametersList, final String requestBodyPath) {
        final HttpRequest request = buildCallBackRequest(BASE_REQUEST_BODY_PATH + requestBodyPath);
        mockCallbackCalls(responsesParametersList, request);
    }
}
